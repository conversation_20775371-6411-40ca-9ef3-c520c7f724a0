const axios = require("axios");
const WebSocket = require("ws");
const crypto = require("crypto");
const config = require("./config");

class BinanceAPI {
  constructor() {
    this.baseUrl = config.binance.baseUrl;
    this.wsUrl = config.binance.wsUrl;
    this.wsApiUrl = config.binance.wsApiUrl;
    this.apiKey = config.binance.apiKey;
    this.apiSecret = config.binance.apiSecret;
    this.recvWindow = config.binance.recvWindow || 5000;
    this.ws = null;
    this.userDataWs = null;
    this.subscriptions = new Map();
    this.requestId = 1;

    // 24-hour reconnection handling for futures WebSocket API
    this.connectionStartTime = null;
    this.reconnectionInterval = null;
    this.userDataCallbacks = null;

    // Validate API credentials if real trading is enabled
    if (config.trading.realTrading.enabled) {
      if (!this.apiKey || !this.apiSecret) {
        throw new Error(
          "Binance API credentials are required for real trading. Please set BINANCE_API_KEY and BINANCE_API_SECRET in your .env file."
        );
      }
      console.log("🔑 Binance API credentials loaded for real trading");
    }
  }

  /**
   * Get kline/candlestick data from Binance REST API
   * @param {string} symbol - Trading pair symbol (e.g., 'BTCUSDT')
   * @param {string} interval - Kline interval (e.g., '1m', '5m', '1h')
   * @param {number} limit - Number of klines to retrieve (max 1000)
   * @returns {Promise<Array>} Array of kline data
   */
  async getKlines(symbol, interval, limit = 500) {
    try {
      // Use production API for market data (public data, no auth needed)
      const url = `https://api.binance.com/api/v3/klines`;
      const params = {
        symbol: symbol.toUpperCase(),
        interval,
        limit,
      };

      const response = await axios.get(url, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching klines for ${symbol}:`, error.message);
      throw error;
    }
  }

  /**
   * Get 24hr ticker statistics
   * @param {string} symbol - Trading pair symbol
   * @returns {Promise<Object>} Ticker data
   */
  async getTicker24hr(symbol) {
    try {
      // Use production API for market data (public data, no auth needed)
      const url = `https://api.binance.com/api/v3/ticker/24hr`;
      const params = { symbol: symbol.toUpperCase() };

      const response = await axios.get(url, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching 24hr ticker for ${symbol}:`, error.message);
      throw error;
    }
  }

  /**
   * Get current price for a symbol
   * @param {string} symbol - Trading pair symbol
   * @returns {Promise<Object>} Price data
   */
  async getPrice(symbol) {
    try {
      // Use production API for market data (public data, no auth needed)
      const url = `https://api.binance.com/api/v3/ticker/price`;
      const params = { symbol: symbol.toUpperCase() };

      const response = await axios.get(url, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching price for ${symbol}:`, error.message);
      throw error;
    }
  }

  /**
   * Connect to Binance WebSocket stream
   * @param {Array<string>} streams - Array of stream names
   * @param {Function} onMessage - Callback for incoming messages
   */
  connectWebSocket(streams, onMessage) {
    if (this.ws) {
      this.ws.close();
    }

    const streamNames = streams.join("/");
    const wsUrl = `${this.wsUrl}/${streamNames}`;

    this.ws = new WebSocket(wsUrl);

    this.ws.on("open", () => {
      console.log("WebSocket connected to Binance");
    });

    this.ws.on("message", (data) => {
      try {
        const message = JSON.parse(data);
        onMessage(message);
      } catch (error) {
        console.error("Error parsing WebSocket message:", error);
      }
    });

    this.ws.on("error", (error) => {
      console.error("WebSocket error:", error);
    });

    this.ws.on("close", () => {
      console.log("WebSocket connection closed");
      // Attempt to reconnect after 5 seconds
      setTimeout(() => {
        console.log("Attempting to reconnect WebSocket...");
        this.connectWebSocket(streams, onMessage);
      }, 5000);
    });
  }

  /**
   * Subscribe to trade streams for multiple symbols
   * @param {Array<string>} symbols - Array of trading pair symbols
   * @param {Function} onTrade - Callback for trade events
   */
  subscribeToTrades(symbols, onTrade) {
    const streams = symbols.map((symbol) => `${symbol.toLowerCase()}@trade`);
    this.connectWebSocket(streams, (message) => {
      if (message.e === "trade") {
        onTrade({
          symbol: message.s,
          price: parseFloat(message.p),
          quantity: parseFloat(message.q),
          time: message.T,
          isBuyerMaker: message.m,
        });
      }
    });
  }

  /**
   * Subscribe to kline streams for multiple symbols
   * @param {Array<string>} symbols - Array of trading pair symbols
   * @param {string} interval - Kline interval
   * @param {Function} onKline - Callback for kline events
   */
  subscribeToKlines(symbols, interval, onKline) {
    const streams = symbols.map(
      (symbol) => `${symbol.toLowerCase()}@kline_${interval}`
    );
    this.connectWebSocket(streams, (message) => {
      if (message.e === "kline") {
        const kline = message.k;
        onKline({
          symbol: kline.s,
          openTime: kline.t,
          closeTime: kline.T,
          open: parseFloat(kline.o),
          high: parseFloat(kline.h),
          low: parseFloat(kline.l),
          close: parseFloat(kline.c),
          volume: parseFloat(kline.v),
          quoteVolume: parseFloat(kline.q),
          trades: kline.n,
          isClosed: kline.x,
        });
      }
    });
  }

  /**
   * Create HMAC SHA256 signature for authenticated requests
   * @param {string} queryString - Query string to sign
   * @returns {string} HMAC signature
   */
  createSignature(queryString) {
    return crypto
      .createHmac("sha256", this.apiSecret)
      .update(queryString)
      .digest("hex");
  }

  /**
   * Create authenticated request headers
   * @returns {Object} Headers object
   */
  getAuthHeaders() {
    return {
      "X-MBX-APIKEY": this.apiKey,
      "Content-Type": "application/x-www-form-urlencoded",
    };
  }

  /**
   * Make authenticated request to Binance API
   * @param {string} endpoint - API endpoint
   * @param {Object} params - Request parameters
   * @param {string} method - HTTP method (GET, POST, DELETE)
   * @returns {Promise<Object>} API response
   */
  async makeAuthenticatedRequest(endpoint, params = {}, method = "GET") {
    try {
      // Add timestamp and recvWindow
      params.timestamp = Date.now();
      params.recvWindow = this.recvWindow;

      // Create query string
      const queryString = new URLSearchParams(params).toString();

      // Create signature
      const signature = this.createSignature(queryString);

      // Add signature to params
      const finalQueryString = `${queryString}&signature=${signature}`;

      const url = `${this.baseUrl}${endpoint}`;
      const headers = this.getAuthHeaders();

      let response;
      if (method === "GET") {
        response = await axios.get(`${url}?${finalQueryString}`, { headers });
      } else if (method === "POST") {
        response = await axios.post(url, finalQueryString, { headers });
      } else if (method === "DELETE") {
        response = await axios.delete(`${url}?${finalQueryString}`, {
          headers,
        });
      }

      return response.data;
    } catch (error) {
      console.error(
        `Error in authenticated request to ${endpoint}:`,
        error.response?.data || error.message
      );
      throw error;
    }
  }

  /**
   * Generate request signature for WebSocket API
   * @param {Object} params - Request parameters
   * @returns {string} Signature
   */
  generateWebSocketSignature(params) {
    const queryString = new URLSearchParams(params).toString();
    return crypto
      .createHmac("sha256", this.apiSecret)
      .update(queryString)
      .digest("hex");
  }

  /**
   * Create authenticated WebSocket API request
   * @param {string} method - API method
   * @param {Object} params - Request parameters
   * @returns {Object} WebSocket request object
   */
  createWebSocketRequest(method, params = {}) {
    const timestamp = Date.now();
    const requestParams = {
      ...params,
      timestamp,
      apiKey: this.apiKey,
    };

    const signature = this.generateWebSocketSignature(requestParams);
    requestParams.signature = signature;

    return {
      id: this.requestId++,
      method: method,
      params: requestParams,
    };
  }

  /**
   * Connect to user data stream for order updates using WebSocket API
   * @param {Function} onOrderUpdate - Callback for order updates
   * @param {Function} onAccountUpdate - Callback for account updates
   */
  async connectUserDataStream(onOrderUpdate, onAccountUpdate) {
    try {
      // Store callbacks for reconnection
      this.userDataCallbacks = { onOrderUpdate, onAccountUpdate };

      // Close existing user data WebSocket if any
      if (this.userDataWs) {
        this.userDataWs.close();
      }

      // Clear existing reconnection interval
      if (this.reconnectionInterval) {
        clearInterval(this.reconnectionInterval);
      }

      console.log(
        "🔄 Connecting to Futures WebSocket API for user data stream..."
      );
      this.userDataWs = new WebSocket(this.wsApiUrl);
      this.connectionStartTime = Date.now();

      this.userDataWs.on("open", () => {
        console.log("📡 Futures WebSocket API connected");

        // Subscribe to user data stream using WebSocket API
        // For userDataStream.start, we only need apiKey
        const subscribeRequest = {
          id: this.requestId++,
          method: "userDataStream.start",
          params: {
            apiKey: this.apiKey,
          },
        };

        console.log("🔄 Starting futures user data stream...");
        this.userDataWs.send(JSON.stringify(subscribeRequest));

        // Set up 24-hour reconnection timer (23.5 hours to be safe)
        this.reconnectionInterval = setInterval(() => {
          const connectionAge = Date.now() - this.connectionStartTime;
          const maxAge = 23.5 * 60 * 60 * 1000; // 23.5 hours in milliseconds

          if (connectionAge >= maxAge) {
            console.log(
              "⏰ WebSocket connection approaching 24-hour limit, reconnecting..."
            );
            this.reconnectUserDataStream();
          }
        }, 60 * 60 * 1000); // Check every hour
      });

      this.userDataWs.on("message", (data) => {
        try {
          const message = JSON.parse(data);

          // Handle WebSocket API responses
          if (message.id && message.status) {
            // This is a response to our request
            if (message.status === 200) {
              console.log("✅ User data stream started successfully");
            } else {
              console.error("❌ User data stream error:", message);
            }
            return;
          }

          // Handle user data stream events
          if (message.stream && message.data) {
            const eventData = message.data;

            if (eventData.e === "executionReport") {
              // Order update event
              const orderUpdate = {
                symbol: eventData.s,
                orderId: eventData.i,
                clientOrderId: eventData.c,
                side: eventData.S,
                orderType: eventData.o,
                timeInForce: eventData.f,
                quantity: parseFloat(eventData.q),
                price: parseFloat(eventData.p),
                stopPrice: parseFloat(eventData.P),
                executedQuantity: parseFloat(eventData.z),
                cumulativeQuoteQuantity: parseFloat(eventData.Z),
                orderStatus: eventData.X,
                orderRejectReason: eventData.r,
                lastExecutedQuantity: parseFloat(eventData.l),
                lastExecutedPrice: parseFloat(eventData.L),
                commissionAmount: parseFloat(eventData.n),
                commissionAsset: eventData.N,
                transactionTime: eventData.T,
                tradeId: eventData.t,
                isOrderWorking: eventData.w,
                isMaker: eventData.m,
                creationTime: eventData.O,
                cumulativeQuoteTransactedQuantity: parseFloat(eventData.Z),
                lastQuoteTransactedQuantity: parseFloat(eventData.Y),
                quoteOrderQuantity: parseFloat(eventData.Q),
              };

              console.log(
                `📊 Order Update: ${orderUpdate.symbol} ${orderUpdate.side} ${orderUpdate.orderStatus} - Order ID: ${orderUpdate.orderId}`
              );
              onOrderUpdate(orderUpdate);
            } else if (eventData.e === "outboundAccountPosition") {
              // Account update event
              const accountUpdate = {
                eventTime: eventData.E,
                lastAccountUpdateTime: eventData.u,
                balances: eventData.B.map((balance) => ({
                  asset: balance.a,
                  free: parseFloat(balance.f),
                  locked: parseFloat(balance.l),
                })),
              };

              console.log("💰 Account Update received");
              onAccountUpdate(accountUpdate);
            }
          }
        } catch (error) {
          console.error("Error parsing user data stream message:", error);
        }
      });

      this.userDataWs.on("error", (error) => {
        console.error("WebSocket API error:", error);
      });

      this.userDataWs.on("close", () => {
        console.log("📡 Futures WebSocket API connection closed");

        // Clear reconnection interval
        if (this.reconnectionInterval) {
          clearInterval(this.reconnectionInterval);
          this.reconnectionInterval = null;
        }

        // Attempt to reconnect after 5 seconds using the reconnection method
        setTimeout(() => {
          console.log("🔄 Attempting to reconnect Futures WebSocket API...");
          this.reconnectUserDataStream();
        }, 5000);
      });
    } catch (error) {
      console.error("Error connecting to WebSocket API:", error.message);
      throw error;
    }
  }

  /**
   * Reconnect user data stream (for 24-hour limit handling)
   */
  async reconnectUserDataStream() {
    if (this.userDataCallbacks) {
      console.log("🔄 Reconnecting user data stream due to 24-hour limit...");
      try {
        await this.connectUserDataStream(
          this.userDataCallbacks.onOrderUpdate,
          this.userDataCallbacks.onAccountUpdate
        );
        console.log("✅ User data stream reconnected successfully");
      } catch (error) {
        console.error(
          "❌ Failed to reconnect user data stream:",
          error.message
        );
        // Retry after 30 seconds
        setTimeout(() => {
          this.reconnectUserDataStream();
        }, 30000);
      }
    }
  }

  /**
   * Close WebSocket connections
   */
  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }

    if (this.userDataWs) {
      this.userDataWs.close();
      this.userDataWs = null;
    }

    // Clear reconnection interval
    if (this.reconnectionInterval) {
      clearInterval(this.reconnectionInterval);
      this.reconnectionInterval = null;
    }

    console.log("🔌 WebSocket connections closed");
  }

  /**
   * Get futures account information
   * @returns {Promise<Object>} Futures account information
   */
  async getAccountInfo() {
    return await this.makeAuthenticatedRequest("/fapi/v2/account");
  }

  /**
   * Set leverage for a futures symbol
   * @param {string} symbol - Trading pair symbol
   * @param {number} leverage - Leverage (1-125)
   * @returns {Promise<Object>} Leverage response
   */
  async setLeverage(symbol, leverage) {
    const params = {
      symbol: symbol.toUpperCase(),
      leverage: leverage.toString(),
    };

    console.log(`⚡ Setting leverage for ${symbol}: ${leverage}x`);
    return await this.makeAuthenticatedRequest(
      "/fapi/v1/leverage",
      params,
      "POST"
    );
  }

  /**
   * Set margin type for a futures symbol
   * @param {string} symbol - Trading pair symbol
   * @param {string} marginType - ISOLATED or CROSSED
   * @returns {Promise<Object>} Margin type response
   */
  async setMarginType(symbol, marginType) {
    const params = {
      symbol: symbol.toUpperCase(),
      marginType: marginType,
    };

    console.log(`🔧 Setting margin type for ${symbol}: ${marginType}`);
    return await this.makeAuthenticatedRequest(
      "/fapi/v1/marginType",
      params,
      "POST"
    );
  }

  /**
   * Get account balance for a specific asset
   * @param {string} asset - Asset symbol (e.g., 'USDT', 'BTC')
   * @returns {Promise<Object>} Balance information
   */
  async getBalance(asset) {
    const accountInfo = await this.getAccountInfo();
    const balance = accountInfo.assets.find((b) => b.asset === asset);
    return balance
      ? {
          asset: balance.asset,
          walletBalance: parseFloat(balance.walletBalance),
          unrealizedProfit: parseFloat(balance.unrealizedProfit),
          marginBalance: parseFloat(balance.marginBalance),
          availableBalance: parseFloat(balance.availableBalance),
        }
      : null;
  }

  /**
   * Place a market buy order
   * @param {string} symbol - Trading pair symbol
   * @param {number} quoteOrderQty - Amount in quote asset (USDT)
   * @returns {Promise<Object>} Order response
   */
  async placeBuyOrder(symbol, quantity) {
    const params = {
      symbol: symbol.toUpperCase(),
      side: "BUY",
      type: "MARKET",
      quantity: quantity.toString(),
    };

    console.log(
      `📈 Placing FUTURES BUY order for ${symbol}: ${quantity} units`
    );
    return await this.makeAuthenticatedRequest(
      "/fapi/v1/order",
      params,
      "POST"
    );
  }

  /**
   * Place a market sell order
   * @param {string} symbol - Trading pair symbol
   * @param {number} quantity - Amount of base asset to sell
   * @returns {Promise<Object>} Order response
   */
  async placeSellOrder(symbol, quantity) {
    const params = {
      symbol: symbol.toUpperCase(),
      side: "SELL",
      type: "MARKET",
      quantity: quantity.toString(),
    };

    console.log(
      `📉 Placing FUTURES SELL order for ${symbol}: ${quantity} units`
    );
    return await this.makeAuthenticatedRequest(
      "/fapi/v1/order",
      params,
      "POST"
    );
  }

  /**
   * Get symbol info for price precision
   * @param {string} symbol - Trading pair symbol
   * @returns {Promise<Object>} Symbol info
   */
  async getSymbolInfo(symbol) {
    try {
      const response = await axios.get(`${this.baseUrl}/fapi/v1/exchangeInfo`, {
        params: { symbol: symbol.toUpperCase() },
      });
      return response.data.symbols[0];
    } catch (error) {
      console.error(
        `Error getting futures symbol info for ${symbol}:`,
        error.message
      );
      throw error;
    }
  }

  /**
   * Format price according to symbol precision
   * @param {number} price - Price to format
   * @param {Object} symbolInfo - Symbol info from exchange
   * @returns {string} Formatted price
   */
  formatPrice(price, symbolInfo) {
    const priceFilter = symbolInfo.filters.find(
      (f) => f.filterType === "PRICE_FILTER"
    );
    if (priceFilter) {
      const tickSize = parseFloat(priceFilter.tickSize);
      const precision = tickSize.toString().split(".")[1]?.length || 0;
      return price.toFixed(precision);
    }
    return price.toFixed(2); // Default to 2 decimal places
  }

  /**
   * Place a stop loss order
   * @param {string} symbol - Trading pair symbol
   * @param {number} quantity - Amount of base asset
   * @param {number} stopPrice - Stop price
   * @returns {Promise<Object>} Order response
   */
  async placeStopLossOrder(symbol, quantity, stopPrice) {
    try {
      // Get symbol info for proper price formatting
      const symbolInfo = await this.getSymbolInfo(symbol);
      const formattedStopPrice = this.formatPrice(stopPrice, symbolInfo);
      const formattedLimitPrice = this.formatPrice(
        stopPrice * 0.99,
        symbolInfo
      );

      const params = {
        symbol: symbol.toUpperCase(),
        side: "SELL",
        type: "STOP_MARKET",
        quantity: quantity.toString(),
        stopPrice: formattedStopPrice,
        timeInForce: "GTC",
      };

      console.log(
        `🛑 Placing FUTURES STOP LOSS order for ${symbol}: ${quantity} units at $${formattedStopPrice}`
      );
      return await this.makeAuthenticatedRequest(
        "/fapi/v1/order",
        params,
        "POST"
      );
    } catch (error) {
      console.error(`Error placing stop loss order:`, error.message);
      throw error;
    }
  }

  /**
   * Place a take profit order
   * @param {string} symbol - Trading pair symbol
   * @param {number} quantity - Amount of base asset
   * @param {number} price - Take profit price
   * @returns {Promise<Object>} Order response
   */
  async placeTakeProfitOrder(symbol, quantity, price) {
    try {
      // Get symbol info for proper price formatting
      const symbolInfo = await this.getSymbolInfo(symbol);
      const formattedPrice = this.formatPrice(price, symbolInfo);

      const params = {
        symbol: symbol.toUpperCase(),
        side: "SELL",
        type: "TAKE_PROFIT_MARKET",
        quantity: quantity.toString(),
        stopPrice: formattedPrice,
        timeInForce: "GTC",
      };

      console.log(
        `💰 Placing FUTURES TAKE PROFIT order for ${symbol}: ${quantity} units at $${formattedPrice}`
      );
      return await this.makeAuthenticatedRequest(
        "/fapi/v1/order",
        params,
        "POST"
      );
    } catch (error) {
      console.error(`Error placing take profit order:`, error.message);
      throw error;
    }
  }

  /**
   * Cancel an order
   * @param {string} symbol - Trading pair symbol
   * @param {number} orderId - Order ID to cancel
   * @returns {Promise<Object>} Cancel response
   */
  async cancelOrder(symbol, orderId) {
    const params = {
      symbol: symbol.toUpperCase(),
      orderId: orderId.toString(),
    };

    console.log(`❌ Cancelling FUTURES order ${orderId} for ${symbol}`);
    return await this.makeAuthenticatedRequest(
      "/fapi/v1/order",
      params,
      "DELETE"
    );
  }

  /**
   * Get order status
   * @param {string} symbol - Trading pair symbol
   * @param {number} orderId - Order ID
   * @returns {Promise<Object>} Order status
   */
  async getOrderStatus(symbol, orderId) {
    const params = {
      symbol: symbol.toUpperCase(),
      orderId: orderId.toString(),
    };

    return await this.makeAuthenticatedRequest("/fapi/v1/order", params, "GET");
  }

  /**
   * Get all open orders for a symbol
   * @param {string} symbol - Trading pair symbol
   * @returns {Promise<Array>} Open orders
   */
  async getOpenOrders(symbol) {
    const params = symbol ? { symbol: symbol.toUpperCase() } : {};
    return await this.makeAuthenticatedRequest(
      "/fapi/v1/openOrders",
      params,
      "GET"
    );
  }

  /**
   * Parse kline data from REST API response
   * @param {Array} klineData - Raw kline data from API
   * @returns {Object} Parsed kline data
   */
  parseKlineData(klineData) {
    return klineData.map((kline) => ({
      openTime: kline[0],
      open: parseFloat(kline[1]),
      high: parseFloat(kline[2]),
      low: parseFloat(kline[3]),
      close: parseFloat(kline[4]),
      volume: parseFloat(kline[5]),
      closeTime: kline[6],
      quoteVolume: parseFloat(kline[7]),
      trades: kline[8],
      takerBuyBaseVolume: parseFloat(kline[9]),
      takerBuyQuoteVolume: parseFloat(kline[10]),
    }));
  }
}

module.exports = BinanceAPI;
