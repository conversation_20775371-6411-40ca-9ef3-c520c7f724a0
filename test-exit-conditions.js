const TradingStrategy = require("./strategy");
const config = require("./config");

// Mock Discord bot for testing
class MockDiscordBot {
  async sendNotification(message) {
    console.log("📢 Discord notification:", message);
  }

  async sendTradingSignal(signalData) {
    console.log("🚨 Trading signal:", signalData);
  }

  async sendPriceAlert(symbol, price, alertType) {
    console.log("⚠️ Price alert:", { symbol, price, alertType });
  }

  async sendProfitLossNotification(orderData, currentPrice, profit) {
    console.log("💰 P&L notification:", { orderData, currentPrice, profit });
  }

  setSimulator(simulator) {
    this.simulator = simulator;
  }
}

async function testExitConditions() {
  console.log("🧪 Testing Take Profit and Stop Loss Conditions\n");

  const discordBot = new MockDiscordBot();
  const strategy = new TradingStrategy(config, discordBot);

  // Create simulator with multiple orders
  const simulator = {
    orders: new Map(),
    lastOrderTime: 0,
    startingBalance: 100000,
    currentBalance: 100000,
    nextOrderId: 1,
    maxConcurrentOrders: 5,
    
    get isHavingOrder() {
      return this.orders.size > 0;
    },
    get order() {
      return this.orders.size > 0 ? Array.from(this.orders.values())[0] : {};
    }
  };

  discordBot.setSimulator(simulator);

  // Test Case 1: Create a test order manually
  console.log("📝 Test Case 1: Manual Order Creation");
  const testOrder1 = {
    id: 1,
    type: "long",
    pair: "BTCUSDT",
    price: 50000,
    stoploss: 49000,
    takeProfit: 52000,
    amount: 1.0,
    timestamp: Date.now(),
  };

  simulator.orders.set(1, testOrder1);
  console.log("✅ Created test order:", testOrder1);
  console.log(`📊 Current orders: ${simulator.orders.size}\n`);

  // Test Case 2: Test Take Profit
  console.log("📝 Test Case 2: Take Profit Trigger");
  console.log("🎯 Simulating price reaching take profit level...");
  strategy.checkPositionExit(testOrder1, 52500, simulator);
  console.log(`📊 Orders after take profit: ${simulator.orders.size}\n`);

  // Test Case 3: Create another order and test Stop Loss
  console.log("📝 Test Case 3: Stop Loss Trigger");
  const testOrder2 = {
    id: 2,
    type: "long",
    pair: "ETHUSDT",
    price: 3000,
    stoploss: 2900,
    takeProfit: 3200,
    amount: 2.0,
    timestamp: Date.now(),
  };

  simulator.orders.set(2, testOrder2);
  console.log("✅ Created second test order:", testOrder2);
  console.log("🎯 Simulating price hitting stop loss...");
  strategy.checkPositionExit(testOrder2, 2850, simulator);
  console.log(`📊 Orders after stop loss: ${simulator.orders.size}\n`);

  // Test Case 4: Test price within range (no exit)
  console.log("📝 Test Case 4: Price Within Range (No Exit)");
  const testOrder3 = {
    id: 3,
    type: "long",
    pair: "SOLUSDT",
    price: 100,
    stoploss: 95,
    takeProfit: 110,
    amount: 10.0,
    timestamp: Date.now(),
  };

  simulator.orders.set(3, testOrder3);
  console.log("✅ Created third test order:", testOrder3);
  console.log("🎯 Simulating price within range...");
  strategy.checkPositionExit(testOrder3, 102, simulator);
  console.log(`📊 Orders after price check: ${simulator.orders.size}\n`);

  // Test Case 5: Test multiple orders with same symbol
  console.log("📝 Test Case 5: Multiple Orders - Same Symbol");
  
  // Simulate handleTradeEvent behavior
  console.log("🔄 Simulating trade event for SOLUSDT at $112 (should trigger take profit)");
  
  // This simulates what happens in handleTradeEvent
  const symbol = "SOLUSDT";
  const price = 112;
  
  if (simulator.orders.size > 0) {
    console.log(`🔄 Checking ${simulator.orders.size} total orders for exit conditions...`);
    
    for (const [, order] of simulator.orders) {
      if (order.pair === symbol) {
        console.log(`🎯 Checking Order #${order.id} (${order.pair}) against price $${price}`);
        strategy.checkPositionExit(order, price, simulator);
      }
    }
  }

  console.log(`📊 Final orders count: ${simulator.orders.size}`);
  console.log(`💰 Final balance: $${simulator.currentBalance}`);
  
  console.log("\n🎉 Exit condition testing completed!");
}

// Run the test
testExitConditions().catch(console.error);
