const WebSocket = require('ws');

// Test script to verify market data WebSocket connectivity
async function testMarketWebSocket() {
  console.log('🧪 Testing Binance Testnet Market Data WebSocket\n');

  const testUrls = [
    'wss://testnet.binance.vision/ws/btcusdt@trade',
    'wss://stream.binance.com:9443/ws/btcusdt@trade',
    'wss://testnet.binance.vision/ws',
  ];

  for (const url of testUrls) {
    console.log(`🔄 Testing: ${url}`);
    
    try {
      const ws = new WebSocket(url);
      
      const timeout = setTimeout(() => {
        console.log(`❌ Timeout connecting to ${url}`);
        ws.close();
      }, 5000);

      ws.on('open', () => {
        clearTimeout(timeout);
        console.log(`✅ Successfully connected to ${url}`);
        
        // If this is the base URL, subscribe to a stream
        if (url.endsWith('/ws')) {
          const subscribeMessage = {
            method: 'SUBSCRIBE',
            params: ['btcusdt@trade'],
            id: 1
          };
          ws.send(JSON.stringify(subscribeMessage));
          console.log('📡 Subscribed to BTCUSDT trade stream');
        }
        
        // Close after 3 seconds
        setTimeout(() => {
          ws.close();
        }, 3000);
      });

      ws.on('message', (data) => {
        try {
          const message = JSON.parse(data);
          if (message.e === 'trade') {
            console.log(`📊 Trade received: ${message.s} at $${message.p}`);
          } else {
            console.log('📨 Message received:', message);
          }
        } catch (error) {
          console.log('📨 Raw message:', data.toString());
        }
      });

      ws.on('error', (error) => {
        clearTimeout(timeout);
        console.log(`❌ Error connecting to ${url}:`, error.message);
      });

      ws.on('close', () => {
        console.log(`🔌 Connection closed for ${url}\n`);
      });

      // Wait for this connection to complete before testing next URL
      await new Promise(resolve => {
        ws.on('close', resolve);
        ws.on('error', resolve);
      });

    } catch (error) {
      console.log(`❌ Failed to create WebSocket for ${url}:`, error.message);
    }
  }

  console.log('🎯 WebSocket testing completed');
}

// Run the test
testMarketWebSocket().catch(console.error);
