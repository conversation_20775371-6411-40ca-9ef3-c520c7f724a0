const DiscordBot = require('./discord-bot');
const TradingStrategy = require('./strategy');
const BinanceAPI = require('./binance-api');

// Test script to verify Discord commands work with real trading system
async function testRealTradingCommands() {
  console.log('🧪 Testing Real Trading Discord Commands\n');

  try {
    // Create instances
    const binanceAPI = new BinanceAPI();
    const discordBot = new DiscordBot();
    const strategy = new TradingStrategy({}, discordBot, binanceAPI);

    // Set strategy reference
    discordBot.setStrategy(strategy);

    console.log('✅ Real trading system initialized successfully');
    console.log('📊 Trading Manager Status:');
    
    const status = strategy.getTradingManagerStatus();
    console.log(`   - Real Trading: ${status.isRealTrading}`);
    console.log(`   - Max Orders: ${status.maxConcurrentOrders}`);
    console.log(`   - Active Orders: ${status.activeOrderCount}`);
    console.log(`   - Current Balance: $${status.currentBalance || 'Loading...'}`);
    console.log(`   - Initial Balance: $${status.startingBalance || 'Loading...'}`);

    console.log('\n🎯 Real trading system is ready for Discord commands!');
    console.log('📱 You can now test these commands in Discord:');
    console.log('   - /balance - Get real Binance Testnet balance');
    console.log('   - /profit - Get real P&L from testnet trades');
    console.log('   - /status - Get live order status from Binance');
    console.log('   - /strategy BTCUSDT - Get current strategy analysis');

    console.log('\n✅ All systems operational - ready for real trading!');

  } catch (error) {
    console.error('❌ Error testing real trading system:', error.message);
    
    if (error.message.includes('API credentials')) {
      console.log('\n🔧 Setup Instructions:');
      console.log('1. Create a .env file with your Binance Testnet credentials:');
      console.log('   BINANCE_API_KEY=your_testnet_api_key');
      console.log('   BINANCE_API_SECRET=your_testnet_secret');
      console.log('   DISCORD_TOKEN=your_discord_bot_token');
      console.log('   DISCORD_CHANNEL_ID=your_discord_channel_id');
      console.log('2. Get testnet credentials from: https://testnet.binance.vision/');
      console.log('3. Make sure you have USDT in your testnet account');
    }
  }
}

// Run the test
testRealTradingCommands().catch(console.error);
