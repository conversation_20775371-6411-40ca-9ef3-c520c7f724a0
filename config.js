require("dotenv").config();

const config = {
  // Discord Bot Configuration
  discord: {
    token: process.env.DISCORD_BOT_TOKEN,
    channelId: process.env.DISCORD_CHANNEL_ID,
    guildId: process.env.DISCORD_GUILD_ID,
  },

  // Binance API Configuration
  binance: {
    // Futures Testnet URLs for safe testing
    baseUrl: "https://testnet.binancefuture.com", // Futures Testnet API
    wsUrl: "wss://stream.binance.com:9443/ws", // Use production WebSocket for market data (safe)
    wsApiUrl: "wss://testnet.binancefuture.com/ws-fapi/v1", // Futures Testnet WebSocket API (correct endpoint)

    // API credentials (from .env file)
    apiKey: process.env.BINANCE_API_KEY,
    apiSecret: process.env.BINANCE_API_SECRET,

    // Trading settings
    useTestnet: true,
    recvWindow: 5000,
  },

  // Trading Configuration
  trading: {
    symbols: ["BTCUSDT", "ETHUSDT", "SOLUSDT", "BNBUSDT", "LTCUSDT"],
    intervals: {
      dataUpdate: 5, // minutes
      priceCheck: 1000, // milliseconds
    },

    // Futures trading settings - REQUIRED (simulation mode removed)
    realTrading: {
      enabled: true, // Always enabled - no simulation fallback
      baseAsset: "USDT", // Margin asset for futures
      tradeAmount: 50, // USDT margin per trade
      leverage: 10, // Leverage for futures trading (1-125x)
      maxConcurrentOrders: 6,
      minOrderInterval: 300000, // 5 minutes in milliseconds
      marginType: "ISOLATED", // ISOLATED or CROSSED
    },
  },

  // Strategy Configuration
  strategy: {
    bollingerBands: {
      period: 20,
      stdDev: 2,
      squeezeThreshold: 0.1, // BBW threshold for squeeze detection
      squeezeHistoryPeriod: 180, // 6 months in days (approximate)
    },
    macd: {
      fastPeriod: 12,
      slowPeriod: 26,
      signalPeriod: 9,
    },
    rsi: {
      period: 14,
      overboughtThreshold: 70,
    },
    volume: {
      period: 20,
      breakoutMultiplier: 1.5, // 50% higher than average
    },
  },

  // Logging Configuration
  logging: {
    level: process.env.LOG_LEVEL || "info",
    enableConsole: true,
    enableFile: false,
  },
};

module.exports = config;
