const DiscordBot = require('./discord-bot');

// Test script to verify Discord notifications are working
async function testDiscordNotification() {
  console.log('🧪 Testing Discord Notifications\n');

  try {
    const discordBot = new DiscordBot();
    
    console.log('📡 Connecting to Discord...');
    await discordBot.connect();
    console.log('✅ Discord connected successfully');

    // Test basic notification
    console.log('📢 Sending test notification...');
    await discordBot.sendNotification('🧪 **Test Notification**\nThis is a test message from the trading bot.');

    // Test trading signal notification
    console.log('🚨 Sending test trading signal...');
    await discordBot.sendTradingSignal({
      type: "BUY",
      symbol: "BTCUSDT",
      price: "45000.00",
      stoploss: "44000.00",
      takeProfit: "47000.00",
      amount: 0.001,
      conditions: {
        bollingerSqueeze: { met: true, value: "Active" },
        macdBullish: { met: true, value: "Bullish" },
        rsiOversold: { met: true, value: "45.2" },
        volumeBreakout: { met: true, value: "High" },
        priceAboveMA: { met: false, value: "Below" },
        signal: "BUY",
        metConditionsCount: 4
      },
      orderId: 999,
      isReal: true
    });

    // Test profit/loss notification
    console.log('💰 Sending test P&L notification...');
    await discordBot.sendProfitLossNotification(
      {
        id: 999,
        type: "long",
        pair: "BTCUSDT",
        price: 45000,
        stoploss: 44000,
        takeProfit: 47000,
        amount: 0.001,
        isReal: true
      },
      47200, // exit price
      120    // profit
    );

    console.log('\n✅ All Discord notifications sent successfully!');
    console.log('📱 Check your Discord channel to verify the messages were received.');

    // Disconnect
    await discordBot.disconnect();
    console.log('🔌 Discord disconnected');

  } catch (error) {
    console.error('❌ Error testing Discord notifications:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Check your .env file has correct Discord credentials');
    console.log('2. Ensure the Discord bot has permissions in your channel');
    console.log('3. Verify the Discord channel ID is correct');
    console.log('4. Make sure the Discord bot is added to your server');
  }
}

// Run the test
testDiscordNotification().catch(console.error);
