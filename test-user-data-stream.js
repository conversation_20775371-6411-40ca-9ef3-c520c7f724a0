const BinanceAPI = require("./binance-api");
const config = require("./config");

// Test script for user data stream functionality
async function testUserDataStream() {
  console.log("🧪 Testing Binance User Data Stream\n");

  // Check if real trading is enabled
  if (!config.trading.realTrading.enabled) {
    console.log(
      "❌ Real trading is not enabled. Set ENABLE_REAL_TRADING=true in .env file"
    );
    console.log(
      "📝 Also ensure you have BINANCE_API_KEY and BINANCE_API_SECRET set"
    );
    return;
  }

  try {
    const binanceAPI = new BinanceAPI();

    console.log("🔑 Testing API credentials...");

    // Test account access
    const accountInfo = await binanceAPI.getAccountInfo();
    console.log("✅ Account info retrieved successfully");
    console.log(`📊 Account has ${accountInfo.balances.length} assets`);

    // Find USDT balance
    const usdtBalance = accountInfo.balances.find((b) => b.asset === "USDT");
    if (usdtBalance) {
      console.log(
        `💰 USDT Balance: ${usdtBalance.free} (Free), ${usdtBalance.locked} (Locked)`
      );
    }

    console.log("\n🔄 Setting up Futures WebSocket API user data stream...");

    // Set up futures user data stream using WebSocket API
    await binanceAPI.connectUserDataStream(
      (orderUpdate) => {
        console.log("\n📊 ORDER UPDATE RECEIVED:");
        console.log(`   Symbol: ${orderUpdate.symbol}`);
        console.log(`   Order ID: ${orderUpdate.orderId}`);
        console.log(`   Side: ${orderUpdate.side}`);
        console.log(`   Type: ${orderUpdate.orderType}`);
        console.log(`   Status: ${orderUpdate.orderStatus}`);
        console.log(`   Quantity: ${orderUpdate.quantity}`);
        console.log(`   Price: ${orderUpdate.price}`);
        console.log(`   Executed Qty: ${orderUpdate.executedQuantity}`);
        console.log(`   Last Executed Price: ${orderUpdate.lastExecutedPrice}`);

        if (orderUpdate.orderStatus === "FILLED") {
          console.log(
            `✅ Order FILLED: ${orderUpdate.side} ${orderUpdate.executedQuantity} ${orderUpdate.symbol} at $${orderUpdate.lastExecutedPrice}`
          );
        }
      },
      (accountUpdate) => {
        console.log("\n💰 ACCOUNT UPDATE RECEIVED:");
        console.log(`   Event Time: ${new Date(accountUpdate.eventTime)}`);
        console.log(
          `   Balances Updated: ${accountUpdate.balances.length} assets`
        );

        // Show USDT balance if updated
        const usdtUpdate = accountUpdate.balances.find(
          (b) => b.asset === "USDT"
        );
        if (usdtUpdate) {
          console.log(
            `   💵 USDT: ${usdtUpdate.free} (Free), ${usdtUpdate.locked} (Locked)`
          );
        }
      }
    );

    console.log(
      "✅ Futures WebSocket API user data stream connected successfully!"
    );
    console.log("\n📝 Instructions for testing:");
    console.log(
      "1. Go to Binance Futures Testnet: https://testnet.binancefuture.com/"
    );
    console.log("2. Place a small test order manually");
    console.log("3. Watch this console for real-time order updates");
    console.log("4. Try placing stop loss and take profit orders");
    console.log(
      "\n⏳ Listening for futures order updates via WebSocket API... (Press Ctrl+C to stop)"
    );

    // Test placing a small order if USDT balance is sufficient
    if (usdtBalance && parseFloat(usdtBalance.free) >= 10) {
      console.log("\n🧪 Testing order placement...");

      try {
        // Get current BTC price
        const btcPrice = await binanceAPI.getPrice("BTCUSDT");
        console.log(`📊 Current BTC price: $${btcPrice.price}`);

        // Place a small test order (10 USDT)
        console.log("📈 Placing test buy order for 10 USDT...");
        const buyOrder = await binanceAPI.placeBuyOrder("BTCUSDT", 10);
        console.log("✅ Test buy order placed:", buyOrder);

        // Calculate stop loss and take profit
        const entryPrice = parseFloat(btcPrice.price);
        const stopLoss = entryPrice * 0.98; // 2% below
        const takeProfit = entryPrice * 1.04; // 4% above
        const quantity = parseFloat(buyOrder.executedQty);

        console.log(`🛑 Placing stop loss at $${stopLoss.toFixed(2)}`);
        const slOrder = await binanceAPI.placeStopLossOrder(
          "BTCUSDT",
          quantity,
          stopLoss
        );
        console.log("✅ Stop loss order placed:", slOrder);

        console.log(`💰 Placing take profit at $${takeProfit.toFixed(2)}`);
        const tpOrder = await binanceAPI.placeTakeProfitOrder(
          "BTCUSDT",
          quantity,
          takeProfit
        );
        console.log("✅ Take profit order placed:", tpOrder);

        console.log("\n🎯 Test orders placed successfully!");
        console.log("📊 You should see order updates in real-time above");
        console.log(
          "⚠️ Remember to cancel these test orders if they don't fill"
        );
      } catch (orderError) {
        console.error("❌ Error placing test orders:", orderError.message);
        console.log(
          "💡 This is normal if you don't have sufficient balance or if orders fail"
        );
      }
    } else {
      console.log(
        "\n💡 Insufficient USDT balance for test orders (need at least 10 USDT)"
      );
      console.log(
        "📝 You can still test by placing orders manually on Binance Testnet"
      );
    }

    // Keep the script running to listen for updates
    process.on("SIGINT", () => {
      console.log("\n🛑 Shutting down user data stream...");
      binanceAPI.disconnect();
      process.exit(0);
    });
  } catch (error) {
    console.error("❌ Error testing user data stream:", error.message);
    console.log("\n🔧 Troubleshooting:");
    console.log("1. Check your .env file has correct API credentials");
    console.log("2. Ensure API keys have trading permissions");
    console.log("3. Verify you're using Binance Testnet credentials");
    console.log("4. Check your internet connection");
  }
}

// Run the test
testUserDataStream().catch(console.error);
