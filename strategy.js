const TechnicalIndicators = require("./indicators");
const TradingManager = require("./trading-manager");

class TradingStrategy {
  constructor(config, discordBot, binanceAPI = null) {
    this.config = config;
    this.discordBot = discordBot;
    this.binanceAPI = binanceAPI;
    this.indicators = new TechnicalIndicators(config);

    // Initialize trading manager
    this.tradingManager = new TradingManager(binanceAPI, discordBot);

    // Store historical data for each symbol
    this.marketData = new Map();
    this.bbwHistory = new Map();
    this.alertStates = new Map();

    // Initialize data structures for tracked symbols
    config.trading.symbols.forEach((symbol) => {
      this.marketData.set(symbol, {
        prices: [],
        volumes: [],
        klines: [],
        lastUpdate: 0,
      });
      this.bbwHistory.set(symbol, []);
      this.alertStates.set(symbol, { alert: false, lastAlertTime: 0 });
    });
  }

  /**
   * Update market data for a symbol
   * @param {string} symbol - Trading pair symbol
   * @param {Array} klineData - Kline data from Binance API
   */
  updateMarketData(symbol, klineData) {
    if (!klineData || klineData.length === 0) return;

    const data = this.marketData.get(symbol);
    if (!data) return;

    // Extract prices and volumes from kline data
    const prices = klineData.map((kline) => kline.close);
    const volumes = klineData.map((kline) => kline.volume);

    // Update stored data
    data.prices = prices;
    data.volumes = volumes;
    data.klines = klineData;
    data.lastUpdate = Date.now();

    // Update BBW history for squeeze detection
    this.updateBBWHistory(symbol, prices);
  }

  /**
   * Update Bollinger Band Width history
   * @param {string} symbol - Trading pair symbol
   * @param {Array<number>} prices - Price data
   */
  updateBBWHistory(symbol, prices) {
    const bollingerBands = this.indicators.calculateBollingerBands(
      prices,
      this.config.strategy.bollingerBands.period,
      this.config.strategy.bollingerBands.stdDev
    );

    if (bollingerBands) {
      const bbw = this.indicators.calculateBollingerBandWidth(bollingerBands);
      if (bbw !== null) {
        const history = this.bbwHistory.get(symbol);
        history.push(bbw);

        // Keep only the last 6 months of data (assuming daily updates)
        const maxHistory =
          this.config.strategy.bollingerBands.squeezeHistoryPeriod;
        if (history.length > maxHistory) {
          history.splice(0, history.length - maxHistory);
        }
      }
    }
  }

  /**
   * Analyze market conditions for a symbol
   * @param {string} symbol - Trading pair symbol
   * @returns {Object} Analysis result
   */
  analyzeSymbol(symbol) {
    const data = this.marketData.get(symbol);
    if (!data || data.prices.length === 0) {
      return { valid: false, reason: "No market data available" };
    }

    const currentPrice = data.prices[data.prices.length - 1];
    const analysis = this.indicators.analyzeMarket({
      prices: data.prices,
      volumes: data.volumes,
      currentPrice,
    });

    if (!analysis.valid) {
      return analysis;
    }

    // Add squeeze detection using historical BBW data
    const bbwHistory = this.bbwHistory.get(symbol);
    const isInSqueeze = this.indicators.isInSqueeze(
      bbwHistory,
      analysis.indicators.bbw,
      this.config.strategy.bollingerBands.squeezeHistoryPeriod
    );

    // Override condition 1 with proper squeeze detection
    analysis.conditions.condition1.met = isInSqueeze;

    // Recalculate if sufficient conditions are met (4 out of 5)
    const conditionValues = Object.values(analysis.conditions)
      .filter((condition) => condition.met !== undefined)
      .map((condition) => condition.met);

    const metConditionsCount = conditionValues.filter((met) => met).length;
    const sufficientConditionsMet = metConditionsCount >= 4;

    analysis.conditions.allConditionsMet = sufficientConditionsMet;
    analysis.conditions.metConditionsCount = metConditionsCount;
    analysis.conditions.signal = sufficientConditionsMet ? "BUY" : "WAIT";

    return {
      ...analysis,
      symbol,
      currentPrice,
      timestamp: Date.now(),
    };
  }

  /**
   * Check for trading signals
   * @param {string} symbol - Trading pair symbol
   * @param {number} currentPrice - Current price
   * @param {Object} simulator - Trading simulator instance (legacy compatibility)
   */
  async checkTradingSignal(symbol, currentPrice, simulator = null) {
    const analysis = this.analyzeSymbol(symbol);

    if (!analysis.valid || analysis.conditions.signal !== "BUY") {
      return;
    }

    // Execute buy order using TradingManager
    await this.executeBuyOrder(symbol, currentPrice, analysis);
  }

  /**
   * Execute a buy order
   * @param {string} symbol - Trading pair symbol
   * @param {number} price - Entry price
   * @param {Object} analysis - Market analysis data
   */
  async executeBuyOrder(symbol, price, analysis) {
    const bollingerBands = analysis.indicators.bollingerBands;
    if (!bollingerBands) return;

    // Calculate stop loss and take profit based on Bollinger Bands
    const bandWidth = bollingerBands.upper - bollingerBands.lower;
    const stopLoss = price - bandWidth * 0.5; // Stop loss below entry
    const takeProfit = price + bandWidth * 1.0; // Take profit above entry

    console.log(`📈 Order calculation for ${symbol}:`, {
      entryPrice: price,
      bollingerUpper: bollingerBands.upper,
      bollingerLower: bollingerBands.lower,
      bandWidth: bandWidth,
      stopLoss: stopLoss,
      takeProfit: takeProfit,
      stopLossDistance: price - stopLoss,
      takeProfitDistance: takeProfit - price,
    });

    try {
      // Execute order through TradingManager
      const order = await this.tradingManager.executeBuyOrder(
        symbol,
        price,
        stopLoss,
        takeProfit
      );

      if (order) {
        // Send Discord notification
        this.discordBot.sendTradingSignal({
          type: "BUY",
          symbol: symbol,
          price: order.price.toFixed(2),
          stoploss: stopLoss.toFixed(2),
          takeProfit: takeProfit.toFixed(2),
          amount: order.amount,
          conditions: analysis.conditions,
          orderId: order.id,
          isReal: order.isReal,
        });

        console.log(
          `BUY signal executed for ${symbol} at $${order.price} (Order ID: ${
            order.id
          }) - ${order.isReal ? "REAL" : "SIMULATED"}`
        );
      }
    } catch (error) {
      console.error(
        `❌ Failed to execute buy order for ${symbol}:`,
        error.message
      );

      // Send error notification to Discord
      this.discordBot.sendNotification(
        `❌ **Order Execution Failed**\n` +
          `Symbol: ${symbol}\n` +
          `Price: $${price}\n` +
          `Error: ${error.message}`
      );
    }
  }

  /**
   * Check price alerts (Bollinger Band extremes)
   * @param {string} symbol - Trading pair symbol
   * @param {number} currentPrice - Current price
   */
  checkPriceAlerts(symbol, currentPrice) {
    const data = this.marketData.get(symbol);
    if (!data || data.prices.length === 0) return;

    const bollingerBands = this.indicators.calculateBollingerBands(
      data.prices,
      this.config.strategy.bollingerBands.period,
      this.config.strategy.bollingerBands.stdDev
    );

    if (!bollingerBands) return;

    const alertState = this.alertStates.get(symbol);
    const threshold = (bollingerBands.middle - bollingerBands.lower) / 2;

    // Check for extreme price movements
    const isExtremeHigh = currentPrice >= bollingerBands.upper + threshold;
    const isExtremeLow = currentPrice <= bollingerBands.lower - threshold;

    if ((isExtremeHigh || isExtremeLow) && !alertState.alert) {
      const alertType = isExtremeHigh
        ? "Extreme High - Above Upper BB"
        : "Extreme Low - Below Lower BB";

      this.discordBot.sendPriceAlert(
        symbol,
        currentPrice.toFixed(2),
        alertType
      );

      alertState.alert = true;
      alertState.lastAlertTime = Date.now();

      console.log(
        `Price alert for ${symbol}: ${alertType} at $${currentPrice}`
      );
    }

    // Reset alert state if price returns to normal range
    if (
      alertState.alert &&
      currentPrice > bollingerBands.lower &&
      currentPrice < bollingerBands.upper
    ) {
      alertState.alert = false;
    }
  }

  /**
   * Check if current position should be closed (for simulation mode)
   * @param {string} symbol - Trading pair symbol
   * @param {number} currentPrice - Current price
   */
  checkPositionExit(symbol, currentPrice) {
    // Use TradingManager to check exits
    this.tradingManager.checkOrderExits(symbol, currentPrice);
  }

  /**
   * Get trading manager status
   * @returns {Object} Trading manager status
   */
  getTradingManagerStatus() {
    return this.tradingManager.getStatus();
  }

  /**
   * Sync real orders with Binance
   */
  async syncRealOrders() {
    await this.tradingManager.syncRealOrders();
  }

  /**
   * Get strategy status for a symbol
   * @param {string} symbol - Trading pair symbol
   * @returns {Object} Strategy status
   */
  async getStrategyStatus(symbol) {
    // Check if we need to fetch data on-demand
    const data = this.marketData.get(symbol);

    // If we don't have data for this symbol and we have a BinanceAPI instance, fetch it
    if ((!data || data.prices.length === 0) && this.binanceAPI) {
      try {
        console.log(`Fetching on-demand data for ${symbol}...`);

        // Initialize data structures for this symbol if needed
        if (!this.marketData.has(symbol)) {
          this.marketData.set(symbol, {
            prices: [],
            volumes: [],
            klines: [],
            lastUpdate: 0,
          });
          this.bbwHistory.set(symbol, []);
          this.alertStates.set(symbol, { alert: false, lastAlertTime: 0 });
        }

        // Fetch historical data
        const klineData = await this.binanceAPI.getKlines(symbol, "5m", 500);
        const parsedData = this.binanceAPI.parseKlineData(klineData);

        // Update market data
        this.updateMarketData(symbol, parsedData);

        console.log(
          `✅ Loaded ${parsedData.length} data points for ${symbol} on-demand`
        );
      } catch (error) {
        console.error(`Error fetching data for ${symbol}:`, error.message);
        return {
          symbol,
          lastUpdate: null,
          analysis: {
            valid: false,
            reason: `Error fetching data: ${error.message}`,
          },
          dataPoints: 0,
          bbwHistoryLength: 0,
        };
      }
    }

    // Now analyze the symbol (with potentially newly fetched data)
    const analysis = this.analyzeSymbol(symbol);
    const updatedData = this.marketData.get(symbol);

    return {
      symbol,
      lastUpdate: updatedData
        ? new Date(updatedData.lastUpdate).toISOString()
        : null,
      analysis,
      dataPoints: updatedData ? updatedData.prices.length : 0,
      bbwHistoryLength: this.bbwHistory.get(symbol)?.length || 0,
    };
  }
}

module.exports = TradingStrategy;
