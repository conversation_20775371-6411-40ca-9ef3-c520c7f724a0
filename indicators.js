const { BollingerBands, MACD, RSI, SMA } = require("technicalindicators");

class TechnicalIndicators {
  constructor(config) {
    this.config = config;
  }

  /**
   * Calculate Bollinger Bands
   * @param {Array<number>} prices - Array of closing prices
   * @param {number} period - Period for calculation
   * @param {number} stdDev - Standard deviation multiplier
   * @returns {Object} Bollinger Bands data
   */
  calculateBollingerBands(prices, period = 20, stdDev = 2) {
    if (prices.length < period) {
      return null;
    }

    const input = {
      period,
      values: prices,
      stdDev,
    };

    const result = BollingerBands.calculate(input);
    return result.length > 0 ? result[result.length - 1] : null;
  }

  /**
   * Calculate Bollinger Band Width (BBW)
   * @param {Object} bollingerBands - Bollinger Bands data
   * @returns {number} Bollinger Band Width
   */
  calculateBollingerBandWidth(bollingerBands) {
    if (!bollingerBands) return null;

    const { upper, lower, middle } = bollingerBands;
    return (upper - lower) / middle;
  }

  /**
   * Check if market is in a squeeze (BBW at 6-month low)
   * @param {Array<number>} bbwHistory - Historical BBW values
   * @param {number} currentBBW - Current BBW value
   * @param {number} lookbackPeriod - Period to check for low (in days)
   * @returns {boolean} True if in squeeze
   */
  isInSqueeze(bbwHistory, currentBBW, lookbackPeriod = 180) {
    if (!bbwHistory || bbwHistory.length < lookbackPeriod || !currentBBW) {
      return false;
    }

    const recentHistory = bbwHistory.slice(-lookbackPeriod);
    const minBBW = Math.min(...recentHistory);

    // Consider it a squeeze if current BBW is within 10% of the minimum
    return currentBBW <= minBBW * 1.1;
  }

  /**
   * Calculate MACD
   * @param {Array<number>} prices - Array of closing prices
   * @param {number} fastPeriod - Fast EMA period
   * @param {number} slowPeriod - Slow EMA period
   * @param {number} signalPeriod - Signal line period
   * @returns {Object} MACD data
   */
  calculateMACD(prices, fastPeriod = 12, slowPeriod = 26, signalPeriod = 9) {
    if (prices.length < slowPeriod + signalPeriod) {
      return null;
    }

    const input = {
      values: prices,
      fastPeriod,
      slowPeriod,
      signalPeriod,
      SimpleMAOscillator: false,
      SimpleMASignal: false,
    };

    const result = MACD.calculate(input);
    return result.length > 0 ? result[result.length - 1] : null;
  }

  /**
   * Calculate RSI
   * @param {Array<number>} prices - Array of closing prices
   * @param {number} period - Period for calculation
   * @returns {number} RSI value
   */
  calculateRSI(prices, period = 14) {
    if (prices.length < period + 1) {
      return null;
    }

    const input = {
      values: prices,
      period,
    };

    const result = RSI.calculate(input);
    return result.length > 0 ? result[result.length - 1] : null;
  }

  /**
   * Calculate Simple Moving Average
   * @param {Array<number>} values - Array of values
   * @param {number} period - Period for calculation
   * @returns {number} SMA value
   */
  calculateSMA(values, period) {
    if (values.length < period) {
      return null;
    }

    const input = {
      values,
      period,
    };

    const result = SMA.calculate(input);
    return result.length > 0 ? result[result.length - 1] : null;
  }

  /**
   * Calculate average volume
   * @param {Array<number>} volumes - Array of volume values
   * @param {number} period - Period for calculation
   * @returns {number} Average volume
   */
  calculateAverageVolume(volumes, period = 20) {
    return this.calculateSMA(volumes, period);
  }

  /**
   * Check if volume confirms breakout
   * @param {number} currentVolume - Current candle volume
   * @param {number} averageVolume - Average volume
   * @param {number} multiplier - Volume multiplier threshold
   * @returns {boolean} True if volume confirms breakout
   */
  isVolumeBreakout(currentVolume, averageVolume, multiplier = 1.5) {
    if (!currentVolume || !averageVolume) return false;
    return currentVolume >= averageVolume * multiplier;
  }

  /**
   * Comprehensive analysis for the new strategy
   * @param {Object} marketData - Market data including prices, volumes, etc.
   * @returns {Object} Analysis result
   */
  analyzeMarket(marketData) {
    const { prices, volumes, currentPrice } = marketData;

    if (!prices || prices.length < 50) {
      return { valid: false, reason: "Insufficient data" };
    }

    // Calculate all indicators
    const bollingerBands = this.calculateBollingerBands(
      prices,
      this.config.strategy.bollingerBands.period,
      this.config.strategy.bollingerBands.stdDev
    );

    const bbw = this.calculateBollingerBandWidth(bollingerBands);

    const macd = this.calculateMACD(
      prices,
      this.config.strategy.macd.fastPeriod,
      this.config.strategy.macd.slowPeriod,
      this.config.strategy.macd.signalPeriod
    );

    const rsi = this.calculateRSI(prices, this.config.strategy.rsi.period);

    const averageVolume = this.calculateAverageVolume(
      volumes,
      this.config.strategy.volume.period
    );

    const currentVolume = volumes[volumes.length - 1];

    return {
      valid: true,
      indicators: {
        bollingerBands,
        bbw,
        macd,
        rsi,
        averageVolume,
        currentVolume,
      },
      conditions: this.checkStrategyConditions({
        bollingerBands,
        bbw,
        macd,
        rsi,
        averageVolume,
        currentVolume,
        currentPrice,
      }),
    };
  }

  /**
   * Check all strategy conditions
   * @param {Object} indicators - All calculated indicators
   * @returns {Object} Conditions check result
   */
  checkStrategyConditions(indicators) {
    const {
      bollingerBands,
      bbw,
      macd,
      rsi,
      averageVolume,
      currentVolume,
      currentPrice,
    } = indicators;

    // Condition 1: Market is in squeeze (BBW at low)
    const condition1 =
      bbw && bbw <= this.config.strategy.bollingerBands.squeezeThreshold;

    // Condition 2: Price breaks above upper Bollinger Band
    const condition2 = bollingerBands && currentPrice > bollingerBands.upper;

    // Condition 3: MACD line above signal line
    const condition3 = macd && macd.MACD > macd.signal;

    // Condition 4: Volume confirms breakout
    const condition4 = this.isVolumeBreakout(
      currentVolume,
      averageVolume,
      this.config.strategy.volume.breakoutMultiplier
    );

    // Condition 5: RSI below 70 (not overextended)
    const condition5 =
      rsi && rsi < this.config.strategy.rsi.overboughtThreshold;

    // Count how many conditions are met
    const conditions = [
      condition1,
      condition2,
      condition3,
      condition4,
      condition5,
    ];
    const metConditionsCount = conditions.filter(
      (condition) => condition
    ).length;

    // Require at least 3 out of 5 conditions to be met
    const sufficientConditionsMet = metConditionsCount >= 3;

    return {
      condition1: {
        met: condition1,
        description: "Market in squeeze (low BBW)",
      },
      condition2: {
        met: condition2,
        description: "Price breaks above upper BB",
      },
      condition3: { met: condition3, description: "MACD above signal line" },
      condition4: { met: condition4, description: "Volume confirms breakout" },
      condition5: { met: condition5, description: "RSI below 70" },
      allConditionsMet: sufficientConditionsMet, // Now means 3/5 conditions met
      metConditionsCount,
      signal: sufficientConditionsMet ? "BUY" : "WAIT",
    };
  }
}

module.exports = TechnicalIndicators;
