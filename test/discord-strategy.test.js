const DiscordBot = require("../discord-bot");
const TradingStrategy = require("../strategy");

// Mock Discord.js components
jest.mock("discord.js", () => ({
  Client: jest.fn().mockImplementation(() => ({
    once: jest.fn(),
    on: jest.fn(),
    login: jest.fn(),
    destroy: jest.fn(),
    user: { tag: "TestBot#1234", id: "123456789" },
    guilds: { cache: new Map() },
    channels: { cache: new Map(), fetch: jest.fn() },
  })),
  GatewayIntentBits: {
    Guilds: 1,
    GuildMessages: 2,
    MessageContent: 4,
  },
  EmbedBuilder: jest.fn().mockImplementation(() => ({
    setTitle: jest.fn().mockReturnThis(),
    setColor: jest.fn().mockReturnThis(),
    setDescription: jest.fn().mockReturnThis(),
    setTimestamp: jest.fn().mockReturnThis(),
    addFields: jest.fn().mockReturnThis(),
  })),
  SlashCommandBuilder: jest.fn().mockImplementation(() => ({
    setName: jest.fn().mockReturnThis(),
    setDescription: jest.fn().mockReturnThis(),
    addStringOption: jest.fn().mockReturnThis(),
    toJSON: jest.fn().mockReturnValue({}),
  })),
  REST: jest.fn().mockImplementation(() => ({
    setToken: jest.fn().mockReturnThis(),
    put: jest.fn(),
  })),
  Routes: {
    applicationGuildCommands: jest.fn(),
  },
}));

// Mock config
jest.mock("../config", () => ({
  discord: {
    token: "test-token",
    channelId: "123456789",
    guildId: "987654321",
  },
  trading: {
    symbols: ["BTCUSDT", "ETHUSDT"],
  },
  strategy: {
    bollingerBands: {
      period: 20,
      stdDev: 2,
      squeezeThreshold: 0.1,
      squeezeHistoryPeriod: 180,
    },
    macd: {
      fastPeriod: 12,
      slowPeriod: 26,
      signalPeriod: 9,
    },
    rsi: {
      period: 14,
      overboughtThreshold: 70,
    },
    volume: {
      period: 20,
      breakoutMultiplier: 1.5,
    },
  },
}));

describe("Discord Strategy Command", () => {
  let discordBot;
  let mockStrategy;
  let mockInteraction;

  beforeEach(() => {
    discordBot = new DiscordBot();

    // Mock strategy
    mockStrategy = {
      getStrategyStatus: jest.fn(),
    };

    // Mock Discord interaction
    mockInteraction = {
      options: {
        getString: jest.fn().mockReturnValue("BTCUSDT"),
      },
      reply: jest.fn(),
    };

    discordBot.setStrategy(mockStrategy);
  });

  test("should handle strategy command with valid analysis", async () => {
    const mockStrategyStatus = {
      analysis: {
        valid: true,
        indicators: {
          bollingerBands: { upper: 45000, middle: 44000, lower: 43000 },
          bbw: 0.045,
          rsi: 65,
          macd: { MACD: 100, signal: 80, histogram: 20 },
          currentVolume: 1500,
          averageVolume: 1000,
        },
        conditions: {
          condition1: { met: true, description: "Market in squeeze (low BBW)" },
          condition2: {
            met: false,
            description: "Price breaks above upper BB",
          },
          condition3: { met: true, description: "MACD above signal line" },
          condition4: { met: true, description: "Volume confirms breakout" },
          condition5: { met: true, description: "RSI below 70" },
          signal: "WAIT",
        },
        currentPrice: 44500,
        timestamp: Date.now(),
      },
      lastUpdate: Date.now(),
      dataPoints: 500,
    };

    mockStrategy.getStrategyStatus.mockResolvedValue(mockStrategyStatus);

    await discordBot.handleStrategyCommand(mockInteraction);

    expect(mockStrategy.getStrategyStatus).toHaveBeenCalledWith("BTCUSDT");
    expect(mockInteraction.reply).toHaveBeenCalledWith({
      embeds: [expect.any(Object)],
    });
  });

  test("should handle strategy command with invalid analysis", async () => {
    const mockStrategyStatus = {
      analysis: {
        valid: false,
        reason: "Insufficient data",
      },
    };

    mockStrategy.getStrategyStatus.mockResolvedValue(mockStrategyStatus);

    await discordBot.handleStrategyCommand(mockInteraction);

    expect(mockInteraction.reply).toHaveBeenCalledWith(
      "❌ No valid analysis available for BTCUSDT\nReason: Insufficient data"
    );
  });

  test("should handle strategy command when strategy not initialized", async () => {
    discordBot.setStrategy(null);

    await discordBot.handleStrategyCommand(mockInteraction);

    expect(mockInteraction.reply).toHaveBeenCalledWith(
      "❌ Strategy not initialized"
    );
  });

  test("should handle strategy command errors gracefully", async () => {
    mockStrategy.getStrategyStatus.mockRejectedValue(new Error("Test error"));

    await discordBot.handleStrategyCommand(mockInteraction);

    expect(mockInteraction.reply).toHaveBeenCalledWith(
      "❌ Error analyzing strategy for BTCUSDT: Test error"
    );
  });
});
