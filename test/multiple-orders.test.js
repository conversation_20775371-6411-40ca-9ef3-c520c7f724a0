const TradingStrategy = require("../strategy");

// Mock Discord bot for testing
class MockDiscordBot {
  constructor() {
    this.notifications = [];
    this.signals = [];
    this.profitLossNotifications = [];
  }

  async sendNotification(message) {
    this.notifications.push(message);
    console.log("Mock Discord notification:", message);
  }

  async sendTradingSignal(signalData) {
    this.signals.push(signalData);
    console.log("Mock trading signal:", signalData);
  }

  async sendPriceAlert(symbol, price, alertType) {
    console.log("Mock price alert:", { symbol, price, alertType });
  }

  async sendProfitLossNotification(orderData, currentPrice, profit) {
    this.profitLossNotifications.push({ orderData, currentPrice, profit });
    console.log("Mock P&L notification:", { orderData, currentPrice, profit });
  }

  setSimulator(simulator) {
    this.simulator = simulator;
  }
}

// Mock config for testing
const mockConfig = {
  trading: {
    symbols: ["BTCUSDT", "ETHUSDT", "SOLUSDT"],
    intervals: {
      dataUpdate: 5,
      priceCheck: 1000,
    },
    simulator: {
      startingBalance: 100000,
      minOrderInterval: 300000,
      maxConcurrentOrders: 5,
    },
  },
  strategy: {
    bollingerBands: {
      period: 20,
      stdDev: 2,
      squeezeThreshold: 0.1,
      squeezeHistoryPeriod: 180,
    },
    macd: {
      fastPeriod: 12,
      slowPeriod: 26,
      signalPeriod: 9,
    },
    rsi: {
      period: 14,
      overboughtThreshold: 70,
    },
    volume: {
      period: 20,
      breakoutMultiplier: 1.5,
    },
  },
};

describe("Multiple Orders Management", () => {
  let discordBot;
  let strategy;
  let simulator;

  beforeEach(() => {
    discordBot = new MockDiscordBot();
    strategy = new TradingStrategy(mockConfig, discordBot);

    // Create a mock simulator that references the trading manager
    simulator = {
      get orders() {
        return strategy.tradingManager.orders;
      },
      get isHavingOrder() {
        return strategy.tradingManager.orders.size > 0;
      },
      get order() {
        return strategy.tradingManager.orders.size > 0
          ? Array.from(strategy.tradingManager.orders.values())[0]
          : {};
      },
      get currentBalance() {
        return strategy.tradingManager.currentBalance;
      },
      get startingBalance() {
        return strategy.tradingManager.startingBalance;
      },
    };

    discordBot.setSimulator(simulator);
  });

  test("should allow multiple concurrent orders", () => {
    // Create multiple orders manually to test the system
    const order1 = {
      id: 1,
      type: "long",
      pair: "BTCUSDT",
      price: 40000,
      stoploss: 39000,
      takeProfit: 42000,
      amount: 2.5,
      timestamp: Date.now(),
    };

    const order2 = {
      id: 2,
      type: "long",
      pair: "ETHUSDT",
      price: 2500,
      stoploss: 2400,
      takeProfit: 2700,
      amount: 8.0,
      timestamp: Date.now(),
    };

    const order3 = {
      id: 3,
      type: "long",
      pair: "SOLUSDT",
      price: 100,
      stoploss: 95,
      takeProfit: 110,
      amount: 20.0,
      timestamp: Date.now(),
    };

    // Add orders to trading manager
    strategy.tradingManager.orders.set(1, order1);
    strategy.tradingManager.orders.set(2, order2);
    strategy.tradingManager.orders.set(3, order3);

    // Verify multiple orders are tracked
    expect(simulator.orders.size).toBe(3);
    expect(simulator.isHavingOrder).toBe(true);

    // Verify each order exists
    expect(simulator.orders.has(1)).toBe(true);
    expect(simulator.orders.has(2)).toBe(true);
    expect(simulator.orders.has(3)).toBe(true);
  });

  test("should close individual orders without affecting others", () => {
    // Set up multiple orders
    const order1 = {
      id: 1,
      type: "long",
      pair: "BTCUSDT",
      price: 40000,
      stoploss: 39000,
      takeProfit: 42000,
      amount: 2.5,
      timestamp: Date.now(),
    };

    const order2 = {
      id: 2,
      type: "long",
      pair: "ETHUSDT",
      price: 2500,
      stoploss: 2400,
      takeProfit: 2700,
      amount: 8.0,
      timestamp: Date.now(),
    };

    // Add isReal property for simulation
    order1.isReal = false;
    order2.isReal = false;

    strategy.tradingManager.orders.set(1, order1);
    strategy.tradingManager.orders.set(2, order2);

    expect(simulator.orders.size).toBe(2);

    // Trigger take profit for order 1
    strategy.tradingManager.checkOrderExit(order1, 42500);

    // Order 1 should be closed, order 2 should remain
    expect(simulator.orders.size).toBe(1);
    expect(simulator.orders.has(1)).toBe(false);
    expect(simulator.orders.has(2)).toBe(true);
    expect(simulator.currentBalance).toBeGreaterThan(100000);
  });

  test("should prevent duplicate orders for same symbol", () => {
    // Add an order for BTCUSDT
    const order1 = {
      id: 1,
      type: "long",
      pair: "BTCUSDT",
      price: 40000,
      stoploss: 39000,
      takeProfit: 42000,
      amount: 2.5,
      timestamp: Date.now(),
    };

    simulator.orders.set(1, order1);
    simulator.lastOrderTime = Date.now() - 400000; // Set last order time to past

    // Try to create another order for BTCUSDT
    // This should be prevented by the checkTradingSignal logic
    strategy.checkTradingSignal("BTCUSDT", 41000, simulator);

    // Should still only have 1 order
    expect(simulator.orders.size).toBe(1);
    expect(discordBot.signals.length).toBe(0); // No new signal should be sent
  });

  test("should respect maximum concurrent orders limit", () => {
    // Fill up to max concurrent orders
    for (let i = 1; i <= 5; i++) {
      const order = {
        id: i,
        type: "long",
        pair: `SYMBOL${i}USDT`,
        price: 1000 * i,
        stoploss: 950 * i,
        takeProfit: 1100 * i,
        amount: 1.0,
        timestamp: Date.now(),
      };
      simulator.orders.set(i, order);
    }

    expect(simulator.orders.size).toBe(5);

    // Try to add another order - should be prevented
    simulator.lastOrderTime = Date.now() - 400000; // Set last order time to past
    strategy.checkTradingSignal("NEWUSDT", 5000, simulator);

    // Should still only have 5 orders
    expect(simulator.orders.size).toBe(5);
    expect(discordBot.signals.length).toBe(0); // No new signal should be sent
  });

  test("should handle stop loss for individual orders", () => {
    // Set up multiple orders
    const order1 = {
      id: 1,
      type: "long",
      pair: "BTCUSDT",
      price: 40000,
      stoploss: 39000,
      takeProfit: 42000,
      amount: 2.5,
      timestamp: Date.now(),
    };

    const order2 = {
      id: 2,
      type: "long",
      pair: "ETHUSDT",
      price: 2500,
      stoploss: 2400,
      takeProfit: 2700,
      amount: 8.0,
      timestamp: Date.now(),
    };

    // Add isReal property for simulation
    order1.isReal = false;
    order2.isReal = false;

    strategy.tradingManager.orders.set(1, order1);
    strategy.tradingManager.orders.set(2, order2);

    const initialBalance = simulator.currentBalance;

    // Trigger stop loss for order 1
    strategy.tradingManager.checkOrderExit(order1, 38500);

    // Order 1 should be closed with a loss, order 2 should remain
    expect(simulator.orders.size).toBe(1);
    expect(simulator.orders.has(1)).toBe(false);
    expect(simulator.orders.has(2)).toBe(true);
    expect(simulator.currentBalance).toBeLessThan(initialBalance);
    expect(discordBot.profitLossNotifications.length).toBe(1);
  });
});
