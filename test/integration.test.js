const BinanceAPI = require("../binance-api");
const TechnicalIndicators = require("../indicators");
const TradingStrategy = require("../strategy");

// Mock Discord bot for testing
class MockDiscordBot {
  async sendNotification(message) {
    console.log("Mock Discord notification:", message);
  }

  async sendTradingSignal(signalData) {
    console.log("Mock trading signal:", signalData);
  }

  async sendPriceAlert(symbol, price, alertType) {
    console.log("Mock price alert:", { symbol, price, alertType });
  }

  async sendProfitLossNotification(orderData, currentPrice, profit) {
    console.log("Mock P&L notification:", { orderData, currentPrice, profit });
  }

  setSimulator(simulator) {
    this.simulator = simulator;
  }
}

// Mock config for testing
const mockConfig = {
  trading: {
    symbols: ["BTCUSDT"],
    intervals: {
      dataUpdate: 5,
      priceCheck: 1000,
    },
    simulator: {
      startingBalance: 100000,
      minOrderInterval: 300000,
    },
  },
  strategy: {
    bollingerBands: {
      period: 20,
      stdDev: 2,
      squeezeThreshold: 0.1,
      squeezeHistoryPeriod: 180,
    },
    macd: {
      fastPeriod: 12,
      slowPeriod: 26,
      signalPeriod: 9,
    },
    rsi: {
      period: 14,
      overboughtThreshold: 70,
    },
    volume: {
      period: 20,
      breakoutMultiplier: 1.5,
    },
  },
};

describe("Integration Tests", () => {
  let binanceAPI;
  let discordBot;
  let strategy;
  let simulator;

  beforeEach(() => {
    binanceAPI = new BinanceAPI();
    discordBot = new MockDiscordBot();
    strategy = new TradingStrategy(mockConfig, discordBot);

    simulator = {
      orders: new Map(),
      lastOrderTime: 0,
      startingBalance: 100000,
      currentBalance: 100000,
      nextOrderId: 1,
      maxConcurrentOrders: 5,

      // Legacy compatibility properties
      get isHavingOrder() {
        return this.orders.size > 0;
      },
      get order() {
        return this.orders.size > 0 ? Array.from(this.orders.values())[0] : {};
      },
    };

    discordBot.setSimulator(simulator);
  });

  describe("Strategy Integration", () => {
    test("should handle market data updates", async () => {
      // Generate mock kline data
      const mockKlineData = Array.from({ length: 100 }, (_, i) => ({
        openTime: Date.now() - (100 - i) * 60000,
        open: 40000 + Math.sin(i * 0.1) * 1000,
        high: 40500 + Math.sin(i * 0.1) * 1000,
        low: 39500 + Math.sin(i * 0.1) * 1000,
        close: 40000 + Math.sin(i * 0.1) * 1000,
        volume: 100 + Math.random() * 50,
        closeTime: Date.now() - (100 - i) * 60000 + 60000,
        quoteVolume: 4000000,
        trades: 1000,
        takerBuyBaseVolume: 50,
        takerBuyQuoteVolume: 2000000,
      }));

      // Update strategy with mock data
      strategy.updateMarketData("BTCUSDT", mockKlineData);

      // Verify data was stored
      const status = await strategy.getStrategyStatus("BTCUSDT");
      expect(status.symbol).toBe("BTCUSDT");
      expect(status.dataPoints).toBe(100);
      expect(status.analysis.valid).toBe(true);
    });

    test("should analyze market conditions", () => {
      // Generate mock data with specific conditions
      const prices = Array.from({ length: 50 }, (_, i) => 40000 + i * 10);
      const volumes = Array.from({ length: 50 }, () => 100);

      const mockKlineData = prices.map((price, i) => ({
        openTime: Date.now() - (50 - i) * 60000,
        open: price - 5,
        high: price + 10,
        low: price - 10,
        close: price,
        volume: volumes[i],
        closeTime: Date.now() - (50 - i) * 60000 + 60000,
        quoteVolume: price * volumes[i],
        trades: 100,
        takerBuyBaseVolume: volumes[i] / 2,
        takerBuyQuoteVolume: (price * volumes[i]) / 2,
      }));

      strategy.updateMarketData("BTCUSDT", mockKlineData);

      const analysis = strategy.analyzeSymbol("BTCUSDT");

      expect(analysis.valid).toBe(true);
      expect(analysis.symbol).toBe("BTCUSDT");
      expect(analysis.indicators).toHaveProperty("bollingerBands");
      expect(analysis.indicators).toHaveProperty("macd");
      expect(analysis.indicators).toHaveProperty("rsi");
      expect(analysis.conditions).toHaveProperty("allConditionsMet");
    });

    test("should handle trading signals", () => {
      // Mock data that should trigger a buy signal
      const prices = Array.from({ length: 50 }, (_, i) => {
        if (i < 40) return 40000; // Stable period for squeeze
        return 40000 + (i - 40) * 100; // Breakout
      });

      const volumes = Array.from({ length: 50 }, (_, i) => {
        if (i < 40) return 100; // Normal volume
        return 200; // High volume on breakout
      });

      const mockKlineData = prices.map((price, i) => ({
        openTime: Date.now() - (50 - i) * 60000,
        open: price - 5,
        high: price + 10,
        low: price - 10,
        close: price,
        volume: volumes[i],
        closeTime: Date.now() - (50 - i) * 60000 + 60000,
        quoteVolume: price * volumes[i],
        trades: 100,
        takerBuyBaseVolume: volumes[i] / 2,
        takerBuyQuoteVolume: (price * volumes[i]) / 2,
      }));

      strategy.updateMarketData("BTCUSDT", mockKlineData);

      // Simulate price check
      const currentPrice = 41000;
      strategy.checkTradingSignal("BTCUSDT", currentPrice, simulator);

      // The signal may or may not trigger depending on exact conditions
      // This test mainly ensures no errors occur during signal checking
      expect(simulator.currentBalance).toBeGreaterThan(0);
    });

    test("should handle position management", () => {
      // Set up an active position in the trading manager
      const order = {
        id: 1,
        type: "long",
        pair: "BTCUSDT",
        price: 40000,
        stoploss: 39000,
        takeProfit: 42000,
        amount: 2.5,
        timestamp: Date.now(),
        isReal: false,
      };

      strategy.tradingManager.orders.set(1, order);

      // Test take profit scenario
      strategy.tradingManager.checkOrderExit(order, 42500);

      // Position should be closed and balance updated
      expect(strategy.tradingManager.orders.size).toBe(0);
      expect(strategy.tradingManager.currentBalance).toBeGreaterThan(100000);
    });
  });

  describe("API Integration", () => {
    test("should parse kline data correctly", () => {
      const rawData = [
        [
          1640995200000,
          "46000.00",
          "47000.00",
          "45000.00",
          "46500.00",
          "100.50",
          1640995259999,
          "4665000.00",
          1500,
          "50.25",
          "2332500.00",
          "0",
        ],
      ];

      const parsed = binanceAPI.parseKlineData(rawData);

      expect(parsed).toHaveLength(1);
      expect(parsed[0].open).toBe(46000);
      expect(parsed[0].close).toBe(46500);
      expect(parsed[0].volume).toBe(100.5);
    });
  });
});

describe("Error Handling", () => {
  test("should handle invalid market data gracefully", () => {
    const discordBot = new MockDiscordBot();
    const strategy = new TradingStrategy(mockConfig, discordBot);

    // Test with empty data
    const analysis = strategy.analyzeSymbol("INVALID");
    expect(analysis.valid).toBe(false);
    expect(analysis.reason).toBe("No market data available");
  });

  test("should handle insufficient data for indicators", () => {
    const discordBot = new MockDiscordBot();
    const strategy = new TradingStrategy(mockConfig, discordBot);

    // Provide insufficient data
    const mockKlineData = Array.from({ length: 5 }, (_, i) => ({
      close: 40000 + i,
      volume: 100,
      openTime: Date.now() - i * 60000,
    }));

    strategy.updateMarketData("BTCUSDT", mockKlineData);

    const analysis = strategy.analyzeSymbol("BTCUSDT");
    expect(analysis.valid).toBe(false);
    expect(analysis.reason).toBe("Insufficient data");
  });
});
