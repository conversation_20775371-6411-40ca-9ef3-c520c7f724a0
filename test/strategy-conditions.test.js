const TechnicalIndicators = require("../indicators");
const config = require("../config");

describe("Strategy Conditions - 3 out of 5 Logic", () => {
  let indicators;

  beforeEach(() => {
    indicators = new TechnicalIndicators(config);
  });

  test("should trigger BUY signal when 3 out of 5 conditions are met", () => {
    const mockIndicators = {
      bollingerBands: { upper: 45000, middle: 44000, lower: 43000 },
      bbw: 0.02, // Below squeeze threshold (condition 1: true)
      macd: { MACD: 100, signal: 80 }, // MACD above signal (condition 3: true)
      rsi: 65, // Below 70 (condition 5: true)
      currentPrice: 42000, // Below upper BB (condition 2: false)
      currentVolume: 800, // Below breakout volume (condition 4: false)
      averageVolume: 1000,
    };

    const result = indicators.checkStrategyConditions(mockIndicators);

    expect(result.condition1.met).toBe(true);
    expect(result.condition2.met).toBe(false);
    expect(result.condition3.met).toBe(true);
    expect(result.condition4.met).toBe(false);
    expect(result.condition5.met).toBe(true);
    expect(result.metConditionsCount).toBe(3);
    expect(result.allConditionsMet).toBe(true);
    expect(result.signal).toBe("BUY");
  });

  test("should trigger WAIT signal when only 2 out of 5 conditions are met", () => {
    const mockIndicators = {
      bollingerBands: { upper: 45000, middle: 44000, lower: 43000 },
      bbw: 0.02, // Below squeeze threshold (condition 1: true)
      macd: { MACD: 80, signal: 100 }, // MACD below signal (condition 3: false)
      rsi: 75, // Above 70 (condition 5: false)
      currentPrice: 42000, // Below upper BB (condition 2: false)
      currentVolume: 1500, // Above breakout volume (condition 4: true)
      averageVolume: 1000,
    };

    const result = indicators.checkStrategyConditions(mockIndicators);

    expect(result.condition1.met).toBe(true);
    expect(result.condition2.met).toBe(false);
    expect(result.condition3.met).toBe(false);
    expect(result.condition4.met).toBe(true);
    expect(result.condition5.met).toBe(false);
    expect(result.metConditionsCount).toBe(2);
    expect(result.allConditionsMet).toBe(false);
    expect(result.signal).toBe("WAIT");
  });

  test("should trigger BUY signal when exactly 3 out of 5 conditions are met", () => {
    const mockIndicators = {
      bollingerBands: { upper: 45000, middle: 44000, lower: 43000 },
      bbw: 0.15, // Above squeeze threshold (condition 1: false)
      macd: { MACD: 100, signal: 80 }, // MACD above signal (condition 3: true)
      rsi: 65, // Below 70 (condition 5: true)
      currentPrice: 46000, // Above upper BB (condition 2: true)
      currentVolume: 800, // Below breakout volume (condition 4: false)
      averageVolume: 1000,
    };

    const result = indicators.checkStrategyConditions(mockIndicators);

    expect(result.condition1.met).toBe(false);
    expect(result.condition2.met).toBe(true);
    expect(result.condition3.met).toBe(true);
    expect(result.condition4.met).toBe(false);
    expect(result.condition5.met).toBe(true);
    expect(result.metConditionsCount).toBe(3);
    expect(result.allConditionsMet).toBe(true);
    expect(result.signal).toBe("BUY");
  });

  test("should trigger BUY signal when all 5 conditions are met", () => {
    const mockIndicators = {
      bollingerBands: { upper: 45000, middle: 44000, lower: 43000 },
      bbw: 0.02, // Below squeeze threshold (condition 1: true)
      macd: { MACD: 100, signal: 80 }, // MACD above signal (condition 3: true)
      rsi: 65, // Below 70 (condition 5: true)
      currentPrice: 46000, // Above upper BB (condition 2: true)
      currentVolume: 1500, // Above breakout volume (condition 4: true)
      averageVolume: 1000,
    };

    const result = indicators.checkStrategyConditions(mockIndicators);

    expect(result.condition1.met).toBe(true);
    expect(result.condition2.met).toBe(true);
    expect(result.condition3.met).toBe(true);
    expect(result.condition4.met).toBe(true);
    expect(result.condition5.met).toBe(true);
    expect(result.metConditionsCount).toBe(5);
    expect(result.allConditionsMet).toBe(true);
    expect(result.signal).toBe("BUY");
  });
});
