# Discord Bot Configuration
DISCORD_BOT_TOKEN=your_discord_bot_token_here
DISCORD_CHANNEL_ID=your_discord_channel_id_here
DISCORD_GUILD_ID=your_discord_guild_id_here

# Binance API Configuration (for real trading)
# Get these from: https://testnet.binance.vision/
BINANCE_API_KEY=your_binance_testnet_api_key_here
BINANCE_API_SECRET=your_binance_testnet_api_secret_here

# Trading Configuration
ENABLE_REAL_TRADING=false  # Set to 'true' to enable real trading on Binance Testnet
LOG_LEVEL=info

# Instructions:
# 1. Copy this file to .env
# 2. Fill in your actual values
# 3. For real trading, create Binance Testnet account at https://testnet.binance.vision/
# 4. Generate API keys with trading permissions
# 5. Set ENABLE_REAL_TRADING=true when ready to test with real orders

# Logging Configuration
LOG_LEVEL=info
