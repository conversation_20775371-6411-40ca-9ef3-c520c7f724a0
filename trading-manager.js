const config = require("./config");

class TradingManager {
  constructor(binanceAPI, discordBot) {
    this.binanceAPI = binanceAPI;
    this.discordBot = discordBot;

    // Validate that we have Binance API access
    if (!this.binanceAPI) {
      throw new Error(
        "Binance API is required - simulation mode has been removed"
      );
    }

    if (!config.binance.apiKey || !config.binance.apiSecret) {
      throw new Error(
        "Binance API credentials are required. Please set BINANCE_API_KEY and BINANCE_API_SECRET in your .env file"
      );
    }

    // Order tracking for real trading only
    this.orders = new Map(); // orderId -> order object
    this.binanceOrderMap = new Map(); // binanceOrderId -> our orderId
    this.nextOrderId = 1;

    // Real trading settings
    this.tradeAmount = config.trading.realTrading.tradeAmount;
    this.maxConcurrentOrders = config.trading.realTrading.maxConcurrentOrders;
    this.lastOrderTime = 0;
    this.minOrderInterval = config.trading.realTrading.minOrderInterval;

    // Account tracking - will be fetched from Binance
    this.initialBalance = null;
    this.currentBalance = null;

    console.log("🎯 Trading Manager initialized: REAL TRADING ONLY mode");
    console.log("📡 Connecting to Binance Testnet API...");

    // Initialize user data stream and account info
    this.initializeRealTrading();
  }

  /**
   * Initialize real trading with Binance API
   */
  async initializeRealTrading() {
    try {
      // Fetch initial account information
      await this.fetchAccountInfo();

      // Initialize user data stream for real-time updates
      await this.initializeUserDataStream();

      console.log("✅ Real trading system initialized successfully");
    } catch (error) {
      console.error(
        "❌ Failed to initialize real trading system:",
        error.message
      );
      throw new Error(
        `Binance Testnet API initialization failed: ${error.message}`
      );
    }
  }

  /**
   * Fetch futures account information from Binance
   */
  async fetchAccountInfo() {
    try {
      console.log("💰 Fetching futures account information from Binance...");
      const accountInfo = await this.binanceAPI.getAccountInfo();

      // Find USDT balance in futures account
      const usdtBalance = accountInfo.assets.find((b) => b.asset === "USDT");
      if (usdtBalance) {
        this.currentBalance = parseFloat(usdtBalance.availableBalance);
        if (this.initialBalance === null) {
          this.initialBalance = this.currentBalance;
        }
        console.log(
          `💰 Futures USDT Balance: ${this.currentBalance} (Available: ${usdtBalance.availableBalance}, Wallet: ${usdtBalance.walletBalance}, Unrealized P&L: ${usdtBalance.unrealizedProfit})`
        );
      } else {
        throw new Error("USDT balance not found in futures account");
      }

      return accountInfo;
    } catch (error) {
      console.error("❌ Failed to fetch futures account info:", error.message);
      throw error;
    }
  }

  /**
   * Initialize user data stream for real-time order updates
   */
  async initializeUserDataStream() {
    try {
      console.log("🔄 Initializing user data stream...");

      await this.binanceAPI.connectUserDataStream(
        (orderUpdate) => this.handleOrderUpdate(orderUpdate),
        (accountUpdate) => this.handleAccountUpdate(accountUpdate)
      );

      console.log("✅ User data stream initialized successfully");
    } catch (error) {
      console.error("❌ Failed to initialize user data stream:", error.message);
      console.log("⚠️ Falling back to polling mode for order updates");
    }
  }

  /**
   * Handle real-time order updates from Binance
   * @param {Object} orderUpdate - Order update from user data stream
   */
  handleOrderUpdate(orderUpdate) {
    const {
      orderId,
      symbol,
      side,
      orderStatus,
      executedQuantity,
      lastExecutedPrice,
    } = orderUpdate;

    // Find our internal order that corresponds to this Binance order
    const internalOrderId = this.binanceOrderMap.get(orderId);
    if (!internalOrderId) {
      // This might be a stop loss or take profit order
      this.handleExitOrderUpdate(orderUpdate);
      return;
    }

    const order = this.orders.get(internalOrderId);
    if (!order) {
      console.log(
        `⚠️ Received update for unknown internal order: ${internalOrderId}`
      );
      return;
    }

    console.log(
      `📊 Order Update: ${symbol} ${side} ${orderStatus} - Internal Order #${internalOrderId}, Binance Order #${orderId}`
    );

    // Update order status
    order.status = orderStatus;
    order.executedQuantity = executedQuantity;

    if (orderStatus === "FILLED" && side === "BUY") {
      // Buy order filled - update the average price
      order.price = lastExecutedPrice;
      console.log(
        `✅ Buy order filled: Order #${internalOrderId} at $${lastExecutedPrice}`
      );

      // Send Discord notification for filled buy order
      this.discordBot.sendNotification(
        `✅ **Buy Order Filled**\n` +
          `Order #${internalOrderId} - ${symbol}\n` +
          `Price: $${lastExecutedPrice}\n` +
          `Quantity: ${executedQuantity}\n` +
          `Stop Loss: $${order.stoploss}\n` +
          `Take Profit: $${order.takeProfit}`
      );
    }
  }

  /**
   * Handle exit order updates (stop loss / take profit)
   * @param {Object} orderUpdate - Order update from user data stream
   */
  handleExitOrderUpdate(orderUpdate) {
    const { orderId, symbol, side, orderStatus, orderType, lastExecutedPrice } =
      orderUpdate;

    if (orderStatus !== "FILLED" || side !== "SELL") {
      return;
    }

    // Find the order that has this stop loss or take profit order ID
    let parentOrder = null;
    let exitType = "";

    for (const [, order] of this.orders) {
      if (order.stopLossOrderId === orderId) {
        parentOrder = order;
        exitType = "Stop Loss";
        break;
      } else if (order.takeProfitOrderId === orderId) {
        parentOrder = order;
        exitType = "Take Profit";
        break;
      }
    }

    if (parentOrder) {
      console.log(
        `🎯 ${exitType} executed for Order #${parentOrder.id}: ${symbol} at $${lastExecutedPrice}`
      );

      // Calculate profit/loss
      const profit =
        parentOrder.amount * (lastExecutedPrice - parentOrder.price);

      // Send Discord notification
      this.discordBot.sendProfitLossNotification(
        parentOrder,
        lastExecutedPrice,
        profit
      );

      // Remove the order from tracking
      this.orders.delete(parentOrder.id);
      this.binanceOrderMap.delete(parentOrder.binanceOrderId);

      // Cancel the other exit order (TP if SL was hit, or SL if TP was hit)
      this.cancelOtherExitOrder(parentOrder, exitType);

      console.log(
        `📊 Order #${
          parentOrder.id
        } closed via ${exitType}. P&L: $${profit.toFixed(2)}`
      );
    } else {
      console.log(
        `⚠️ Received exit order update for unknown order: Binance Order #${orderId}`
      );
    }
  }

  /**
   * Cancel the other exit order when one is filled
   * @param {Object} order - The parent order
   * @param {string} filledExitType - Which exit order was filled ('Stop Loss' or 'Take Profit')
   */
  async cancelOtherExitOrder(order, filledExitType) {
    try {
      if (filledExitType === "Stop Loss" && order.takeProfitOrderId) {
        await this.binanceAPI.cancelOrder(order.pair, order.takeProfitOrderId);
        console.log(
          `❌ Cancelled Take Profit order ${order.takeProfitOrderId} for Order #${order.id}`
        );
      } else if (filledExitType === "Take Profit" && order.stopLossOrderId) {
        await this.binanceAPI.cancelOrder(order.pair, order.stopLossOrderId);
        console.log(
          `❌ Cancelled Stop Loss order ${order.stopLossOrderId} for Order #${order.id}`
        );
      }
    } catch (error) {
      console.error(`⚠️ Failed to cancel other exit order:`, error.message);
    }
  }

  /**
   * Handle futures account updates from Binance
   * @param {Object} accountUpdate - Account update from user data stream
   */
  handleAccountUpdate(accountUpdate) {
    console.log("💰 Futures account balance updated");

    // Update USDT balance from real-time futures data
    if (accountUpdate.a) {
      // Futures account update format
      const usdtBalance = accountUpdate.a.find((b) => b.a === "USDT");
      if (usdtBalance) {
        this.currentBalance = parseFloat(usdtBalance.wb); // wallet balance
        console.log(`💰 Updated Futures USDT Balance: ${this.currentBalance}`);
      }
    }
  }

  /**
   * Execute a buy order (real or simulated)
   * @param {string} symbol - Trading pair symbol
   * @param {number} currentPrice - Current market price
   * @param {number} stopLoss - Stop loss price
   * @param {number} takeProfit - Take profit price
   * @returns {Promise<Object>} Order result
   */
  async executeBuyOrder(symbol, currentPrice, stopLoss, takeProfit) {
    try {
      // Check if we can place a new order
      if (!this.canPlaceOrder(symbol)) {
        return null;
      }

      const orderId = this.nextOrderId++;
      this.lastOrderTime = Date.now();

      // Execute real buy order on Binance Testnet
      return await this.executeRealBuyOrder(
        orderId,
        symbol,
        currentPrice,
        stopLoss,
        takeProfit
      );
    } catch (error) {
      console.error(
        `❌ Error executing buy order for ${symbol}:`,
        error.message
      );
      throw error;
    }
  }

  /**
   * Execute a real buy order on Binance
   */
  async executeRealBuyOrder(
    orderId,
    symbol,
    currentPrice,
    stopLoss,
    takeProfit
  ) {
    console.log(`🚀 Executing REAL FUTURES buy order for ${symbol}`);

    // Check futures account balance
    const balance = await this.binanceAPI.getBalance("USDT");
    if (!balance || balance.availableBalance < this.tradeAmount) {
      throw new Error(
        `Insufficient USDT balance for futures trading. Available: ${
          balance?.availableBalance || 0
        }, Required: ${this.tradeAmount}`
      );
    }

    // Calculate quantity for futures (quantity = margin / price / leverage)
    const leverage = config.trading.realTrading.leverage || 10;
    const notionalValue = this.tradeAmount * leverage;
    const quantity = (notionalValue / currentPrice).toFixed(3);

    console.log(
      `📊 Futures Order Details: Margin: $${this.tradeAmount}, Leverage: ${leverage}x, Quantity: ${quantity}`
    );

    // Set leverage and margin type for the symbol
    try {
      await this.binanceAPI.setLeverage(symbol, leverage);
      await this.binanceAPI.setMarginType(
        symbol,
        config.trading.realTrading.marginType || "ISOLATED"
      );
    } catch (error) {
      console.log(
        `⚠️ Note: Could not set leverage/margin (may already be set): ${error.message}`
      );
    }

    // Place futures market buy order
    const buyOrderResult = await this.binanceAPI.placeBuyOrder(
      symbol,
      quantity
    );

    // Calculate actual quantity purchased
    const executedQty = parseFloat(buyOrderResult.executedQty);
    const avgPrice = parseFloat(
      buyOrderResult.fills.reduce(
        (sum, fill) => sum + parseFloat(fill.price) * parseFloat(fill.qty),
        0
      ) / executedQty
    );

    // Create order tracking object
    const order = {
      id: orderId,
      binanceOrderId: buyOrderResult.orderId,
      type: "long",
      pair: symbol,
      price: avgPrice,
      stoploss: stopLoss,
      takeProfit: takeProfit,
      amount: executedQty,
      timestamp: Date.now(),
      isReal: true,
      status: "FILLED",
      stopLossOrderId: null,
      takeProfitOrderId: null,
    };

    // Store order and create mapping
    this.orders.set(orderId, order);
    this.binanceOrderMap.set(buyOrderResult.orderId, orderId);

    // Place stop loss and take profit orders
    try {
      const stopLossOrder = await this.binanceAPI.placeStopLossOrder(
        symbol,
        executedQty,
        stopLoss
      );
      order.stopLossOrderId = stopLossOrder.orderId;

      const takeProfitOrder = await this.binanceAPI.placeTakeProfitOrder(
        symbol,
        executedQty,
        takeProfit
      );
      order.takeProfitOrderId = takeProfitOrder.orderId;

      console.log(`✅ Real order placed successfully:`);
      console.log(
        `   📈 Buy Order #${orderId} (Binance: ${buyOrderResult.orderId})`
      );
      console.log(`   🛑 Stop Loss: ${stopLossOrder.orderId} at $${stopLoss}`);
      console.log(
        `   💰 Take Profit: ${takeProfitOrder.orderId} at $${takeProfit}`
      );
    } catch (error) {
      console.error(
        `⚠️ Error placing stop loss/take profit orders:`,
        error.message
      );
      // Order is still valid, just without automatic exit orders
    }

    return order;
  }

  /**
   * Check if we can place a new order
   */
  canPlaceOrder(symbol) {
    // Check maximum concurrent orders
    if (this.orders.size >= this.maxConcurrentOrders) {
      console.log(
        `⚠️ Maximum concurrent orders reached: ${this.orders.size}/${this.maxConcurrentOrders}`
      );
      return false;
    }

    // Check if we already have an order for this symbol
    const hasOrderForSymbol = Array.from(this.orders.values()).some(
      (order) => order.pair === symbol
    );
    if (hasOrderForSymbol) {
      console.log(`⚠️ Already have an active order for ${symbol}`);
      return false;
    }

    // Check minimum time between orders
    const timeSinceLastOrder = Date.now() - this.lastOrderTime;
    if (timeSinceLastOrder < this.minOrderInterval) {
      console.log(
        `⚠️ Too soon since last order: ${timeSinceLastOrder}ms < ${this.minOrderInterval}ms`
      );
      return false;
    }

    return true;
  }

  /**
   * Check if any orders should be closed (for simulation mode)
   */
  checkOrderExits(symbol, currentPrice) {
    const ordersToCheck = Array.from(this.orders.values()).filter(
      (order) => order.pair === symbol && !order.isReal
    );

    for (const order of ordersToCheck) {
      this.checkOrderExit(order, currentPrice);
    }
  }

  /**
   * Check if a specific order should be closed (simulation only)
   */
  checkOrderExit(order, currentPrice) {
    if (order.isReal) {
      // Real orders are handled by Binance stop loss/take profit orders
      return;
    }

    let shouldClose = false;
    let exitReason = "";

    if (order.type === "long") {
      if (currentPrice >= order.takeProfit) {
        shouldClose = true;
        exitReason = "Take Profit";
      } else if (currentPrice <= order.stoploss) {
        shouldClose = true;
        exitReason = "Stop Loss";
      }
    }

    if (shouldClose) {
      this.closeOrder(order, currentPrice, exitReason);
    }
  }

  /**
   * Close an order (simulation only)
   */
  closeOrder(order, exitPrice, reason) {
    if (order.isReal) {
      console.log(
        `⚠️ Cannot manually close real order #${order.id}. It's managed by Binance.`
      );
      return;
    }

    // Calculate profit/loss for simulation
    let profit = order.amount * (exitPrice - order.price);
    if (order.type === "short") {
      profit = -profit;
    }

    // Update simulated balance
    this.currentBalance += profit;

    // Send Discord notification
    this.discordBot.sendProfitLossNotification(order, exitPrice, profit);

    // Remove order
    this.orders.delete(order.id);

    console.log(
      `Position closed for ${order.pair} (Order ID: ${
        order.id
      }): ${reason} at $${exitPrice}, P&L: $${profit.toFixed(2)}`
    );
  }

  /**
   * Get current status
   */
  getStatus() {
    return {
      isRealTrading: true, // Always real trading now
      orders: this.orders,
      activeOrderCount: this.orders.size,
      maxConcurrentOrders: this.maxConcurrentOrders,
      currentBalance: this.currentBalance || 0,
      startingBalance: this.initialBalance || 0,

      // Legacy compatibility
      get isHavingOrder() {
        return this.orders.size > 0;
      },
      get order() {
        return this.orders.size > 0 ? Array.from(this.orders.values())[0] : {};
      },
    };
  }

  /**
   * Sync real orders with Binance (fallback method if user data stream fails)
   * This is now mainly used as a backup mechanism
   */
  async syncRealOrders() {
    if (!this.isRealTrading) return;

    console.log("🔄 Performing backup sync of real orders...");
    const realOrders = Array.from(this.orders.values()).filter(
      (order) => order.isReal
    );

    if (realOrders.length === 0) {
      return;
    }

    for (const order of realOrders) {
      try {
        // Check if stop loss or take profit orders are filled
        if (order.stopLossOrderId) {
          const slStatus = await this.binanceAPI.getOrderStatus(
            order.pair,
            order.stopLossOrderId
          );
          if (slStatus.status === "FILLED") {
            console.log(
              `🛑 Stop Loss filled for Order #${order.id} (detected via polling)`
            );
            await this.handleRealOrderExit(order, "Stop Loss");
            continue;
          }
        }

        if (order.takeProfitOrderId) {
          const tpStatus = await this.binanceAPI.getOrderStatus(
            order.pair,
            order.takeProfitOrderId
          );
          if (tpStatus.status === "FILLED") {
            console.log(
              `💰 Take Profit filled for Order #${order.id} (detected via polling)`
            );
            await this.handleRealOrderExit(order, "Take Profit");
            continue;
          }
        }
      } catch (error) {
        console.error(`Error syncing order #${order.id}:`, error.message);
      }
    }
  }

  /**
   * Handle real order exit
   */
  async handleRealOrderExit(order, reason) {
    // Get current balance to calculate profit
    const currentBalance = await this.binanceAPI.getBalance("USDT");

    // Remove order from tracking
    this.orders.delete(order.id);

    // Send notification
    this.discordBot.sendProfitLossNotification(order, 0, 0); // Real P&L calculated by Binance

    console.log(
      `Real position closed for ${order.pair} (Order ID: ${order.id}): ${reason}`
    );
  }
}

module.exports = TradingManager;
